<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
  "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">


<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    
    <title>User Guide &#8212; phpMyAdmin 4.8.5 documentation</title>
    
    <link rel="stylesheet" href="_static/classic.css" type="text/css" />
    <link rel="stylesheet" href="_static/pygments.css" type="text/css" />
    
    <script type="text/javascript">
      var DOCUMENTATION_OPTIONS = {
        URL_ROOT:    './',
        VERSION:     '4.8.5',
        COLLAPSE_INDEX: false,
        FILE_SUFFIX: '.html',
        HAS_SOURCE:  true
      };
    </script>
    <script type="text/javascript" src="_static/jquery.js"></script>
    <script type="text/javascript" src="_static/underscore.js"></script>
    <script type="text/javascript" src="_static/doctools.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="copyright" title="Copyright" href="copyright.html" />
    <link rel="top" title="phpMyAdmin 4.8.5 documentation" href="index.html" />
    <link rel="next" title="Configuring phpMyAdmin" href="settings.html" />
    <link rel="prev" title="Configuration" href="config.html" /> 
  </head>
  <body role="document">
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="settings.html" title="Configuring phpMyAdmin"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="config.html" title="Configuration"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">phpMyAdmin 4.8.5 documentation</a> &#187;</li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <div class="section" id="user-guide">
<h1>User Guide<a class="headerlink" href="#user-guide" title="Permalink to this headline">¶</a></h1>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="settings.html">Configuring phpMyAdmin</a></li>
<li class="toctree-l1"><a class="reference internal" href="two_factor.html">Two-factor authentication</a><ul>
<li class="toctree-l2"><a class="reference internal" href="two_factor.html#authentication-application-2fa">Authentication Application (2FA)</a></li>
<li class="toctree-l2"><a class="reference internal" href="two_factor.html#hardware-security-key-fido-u2f">Hardware Security Key (FIDO U2F)</a></li>
<li class="toctree-l2"><a class="reference internal" href="two_factor.html#simple-two-factor-authentication">Simple two-factor authentication</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="transformations.html">Transformations</a><ul>
<li class="toctree-l2"><a class="reference internal" href="transformations.html#introduction">Introduction</a></li>
<li class="toctree-l2"><a class="reference internal" href="transformations.html#usage">Usage</a></li>
<li class="toctree-l2"><a class="reference internal" href="transformations.html#file-structure">File structure</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="bookmarks.html">Bookmarks</a><ul>
<li class="toctree-l2"><a class="reference internal" href="bookmarks.html#storing-bookmarks">Storing bookmarks</a></li>
<li class="toctree-l2"><a class="reference internal" href="bookmarks.html#variables-inside-bookmarks">Variables inside bookmarks</a></li>
<li class="toctree-l2"><a class="reference internal" href="bookmarks.html#browsing-table-using-bookmark">Browsing table using bookmark</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="privileges.html">User management</a><ul>
<li class="toctree-l2"><a class="reference internal" href="privileges.html#creating-a-new-user">Creating a new user</a></li>
<li class="toctree-l2"><a class="reference internal" href="privileges.html#editing-an-existing-user">Editing an existing user</a></li>
<li class="toctree-l2"><a class="reference internal" href="privileges.html#deleting-a-user">Deleting a user</a></li>
<li class="toctree-l2"><a class="reference internal" href="privileges.html#assigning-privileges-to-user-for-a-specific-database">Assigning privileges to user for a specific database</a></li>
<li class="toctree-l2"><a class="reference internal" href="privileges.html#configurable-menus-and-user-groups">Configurable menus and user groups</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="relations.html">Relations</a><ul>
<li class="toctree-l2"><a class="reference internal" href="relations.html#technical-info">Technical info</a></li>
<li class="toctree-l2"><a class="reference internal" href="relations.html#relation-view">Relation view</a></li>
<li class="toctree-l2"><a class="reference internal" href="relations.html#designer">Designer</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="charts.html">Charts</a><ul>
<li class="toctree-l2"><a class="reference internal" href="charts.html#chart-implementation">Chart implementation</a></li>
<li class="toctree-l2"><a class="reference internal" href="charts.html#examples">Examples</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="import_export.html">Import and export</a><ul>
<li class="toctree-l2"><a class="reference internal" href="import_export.html#import">Import</a></li>
<li class="toctree-l2"><a class="reference internal" href="import_export.html#export">Export</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="themes.html">Custom Themes</a><ul>
<li class="toctree-l2"><a class="reference internal" href="themes.html#configuration">Configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="themes.html#creating-custom-theme">Creating custom theme</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="other.html">Other sources of information</a><ul>
<li class="toctree-l2"><a class="reference internal" href="other.html#printed-book">Printed Book</a></li>
<li class="toctree-l2"><a class="reference internal" href="other.html#tutorials">Tutorials</a></li>
</ul>
</li>
</ul>
</div>
</div>


          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <h4>Previous topic</h4>
  <p class="topless"><a href="config.html"
                        title="previous chapter">Configuration</a></p>
  <h4>Next topic</h4>
  <p class="topless"><a href="settings.html"
                        title="next chapter">Configuring phpMyAdmin</a></p>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="_sources/user.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3>Quick search</h3>
    <form class="search" action="search.html" method="get">
      <div><input type="text" name="q" /></div>
      <div><input type="submit" value="Go" /></div>
      <input type="hidden" name="check_keywords" value="yes" />
      <input type="hidden" name="area" value="default" />
    </form>
</div>
<script type="text/javascript">$('#searchbox').show(0);</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="settings.html" title="Configuring phpMyAdmin"
             >next</a> |</li>
        <li class="right" >
          <a href="config.html" title="Configuration"
             >previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">phpMyAdmin 4.8.5 documentation</a> &#187;</li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
        &#169; <a href="copyright.html">Copyright</a> 2012 - 2018, The phpMyAdmin devel team.
      Created using <a href="http://sphinx-doc.org/">Sphinx</a> 1.4.9.
    </div>
  </body>
</html>