{% if submit_attribute is defined and submit_attribute != false %}
    {% set attribute = submit_attribute %}
{% elseif column_meta['Extra'] is defined
    and column_meta['Extra'] == 'on update CURRENT_TIMESTAMP' %}
    {% set attribute = 'on update CURRENT_TIMESTAMP' %}
{% elseif extracted_columnspec['attribute'] is defined %}
    {% set attribute = extracted_columnspec['attribute'] %}
{% else %}
    {% set attribute = '' %}
{% endif %}
{% set attribute = attribute|upper %}
<select name="field_attribute[{{ column_number }}]"
    id="field_{{ column_number }}_{{ ci - ci_offset }}">
    {% set cnt_attribute_types = attribute_types|length - 1 %}
    {% for i in 0..cnt_attribute_types %}
        <option value="{{ attribute_types[i] }}"
            {{- attribute == attribute_types[i]|upper ? ' selected="selected"' }}>
            {{ attribute_types[i] }}
        </option>
    {% endfor %}
</select>
