<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
  "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">


<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    
    <title>Credits &#8212; phpMyAdmin 4.8.5 documentation</title>
    
    <link rel="stylesheet" href="_static/classic.css" type="text/css" />
    <link rel="stylesheet" href="_static/pygments.css" type="text/css" />
    
    <script type="text/javascript">
      var DOCUMENTATION_OPTIONS = {
        URL_ROOT:    './',
        VERSION:     '4.8.5',
        COLLAPSE_INDEX: false,
        FILE_SUFFIX: '.html',
        HAS_SOURCE:  true
      };
    </script>
    <script type="text/javascript" src="_static/jquery.js"></script>
    <script type="text/javascript" src="_static/underscore.js"></script>
    <script type="text/javascript" src="_static/doctools.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="copyright" title="Copyright" href="copyright.html" />
    <link rel="top" title="phpMyAdmin 4.8.5 documentation" href="index.html" />
    <link rel="next" title="Glossary" href="glossary.html" />
    <link rel="prev" title="Copyright" href="copyright.html" /> 
  </head>
  <body role="document">
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="glossary.html" title="Glossary"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="copyright.html" title="Copyright"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">phpMyAdmin 4.8.5 documentation</a> &#187;</li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <div class="section" id="credits">
<span id="id1"></span><h1>Credits<a class="headerlink" href="#credits" title="Permalink to this headline">¶</a></h1>
<div class="section" id="credits-in-chronological-order">
<h2>Credits, in chronological order<a class="headerlink" href="#credits-in-chronological-order" title="Permalink to this headline">¶</a></h2>
<ul class="simple">
<li>Tobias Ratschiller &lt;tobias_at_ratschiller.com&gt;<ul>
<li>creator of the phpmyadmin project</li>
<li>maintainer from 1998 to summer 2000</li>
</ul>
</li>
<li>Marc Delisle &lt;marc_at_infomarc.info&gt;<ul>
<li>multi-language version in December 1998</li>
<li>various fixes and improvements</li>
<li>first version of the <a class="reference internal" href="glossary.html#term-sql"><span class="xref std std-term">SQL</span></a> analyser (most of it)</li>
<li>maintainer from 2001 to 2015</li>
</ul>
</li>
<li>Olivier Müller &lt;om_at_omnis.ch&gt;<ul>
<li>started SourceForge phpMyAdmin project in March 2001</li>
<li>sync&#8217;ed different existing CVS trees with new features and bugfixes</li>
<li>multi-language improvements, dynamic language selection</li>
<li>many bugfixes and improvements</li>
</ul>
</li>
<li>Loïc Chapeaux &lt;lolo_at_phpheaven.net&gt;<ul>
<li>rewrote and optimized JavaScript, DHTML and DOM stuff</li>
<li>rewrote the scripts so they fit the <a class="reference internal" href="glossary.html#term-pear"><span class="xref std std-term">PEAR</span></a> coding standards and
generate XHTML1.0 and CSS2 compliant codes</li>
<li>improved the language detection system</li>
<li>many bugfixes and improvements</li>
</ul>
</li>
<li>Robin Johnson &lt;robbat2_at_users.sourceforge.net&gt;<ul>
<li>database maintenance controls</li>
<li>table type code</li>
<li>Host authentication <a class="reference internal" href="glossary.html#term-ip"><span class="xref std std-term">IP</span></a> Allow/Deny</li>
<li>DB-based configuration (Not completed)</li>
<li><a class="reference internal" href="glossary.html#term-sql"><span class="xref std std-term">SQL</span></a> parser and pretty-printer</li>
<li><a class="reference internal" href="glossary.html#term-sql"><span class="xref std std-term">SQL</span></a> validator</li>
<li>many bugfixes and improvements</li>
</ul>
</li>
<li>Armel Fauveau &lt;armel.fauveau_at_globalis-ms.com&gt;<ul>
<li>bookmarks feature</li>
<li>multiple dump feature</li>
<li>gzip dump feature</li>
<li>zip dump feature</li>
</ul>
</li>
<li>Geert Lund &lt;glund_at_silversoft.dk&gt;<ul>
<li>various fixes</li>
<li>moderator of the phpMyAdmin former users forum at phpwizard.net</li>
</ul>
</li>
<li>Korakot Chaovavanich &lt;korakot_at_iname.com&gt;<ul>
<li>&#8220;insert as new row&#8221; feature</li>
</ul>
</li>
<li>Pete Kelly &lt;webmaster_at_trafficg.com&gt;<ul>
<li>rewrote and fix dump code</li>
<li>bugfixes</li>
</ul>
</li>
<li>Steve Alberty &lt;alberty_at_neptunlabs.de&gt;<ul>
<li>rewrote dump code for PHP4</li>
<li>mySQL table statistics</li>
<li>bugfixes</li>
</ul>
</li>
<li>Benjamin Gandon &lt;gandon_at_isia.cma.fr&gt;<ul>
<li>main author of the version *******</li>
<li>bugfixes</li>
</ul>
</li>
<li>Alexander M. Turek &lt;me_at_derrabus.de&gt;<ul>
<li>MySQL 4.0 / 4.1 / 5.0 compatibility</li>
<li>abstract database interface (PMA_DBI) with MySQLi support</li>
<li>privileges administration</li>
<li><a class="reference internal" href="glossary.html#term-xml"><span class="xref std std-term">XML</span></a> exports</li>
<li>various features and fixes</li>
<li>German language file updates</li>
</ul>
</li>
<li>Mike Beck &lt;mike.beck_at_web.de&gt;<ul>
<li>automatic joins in QBE</li>
<li>links column in printview</li>
<li>Relation view</li>
</ul>
</li>
<li>Michal Čihař &lt;michal_at_cihar.com&gt;<ul>
<li>enhanced index creation/display feature</li>
<li>feature to use a different charset for HTML than for MySQL</li>
<li>improvements of export feature</li>
<li>various features and fixes</li>
<li>Czech language file updates</li>
<li>created current website for phpMyAdmin</li>
</ul>
</li>
<li>Christophe Gesché from the &#8220;MySQL Form Generator for PHPMyAdmin&#8221;
(<a class="reference external" href="https://sourceforge.net/projects/phpmysqlformgen/">https://sourceforge.net/projects/phpmysqlformgen/</a>)<ul>
<li>suggested the patch for multiple table printviews</li>
</ul>
</li>
<li>Garvin Hicking &lt;me_at_supergarv.de&gt;<ul>
<li>built the patch for vertical display of table rows</li>
<li>built the Javascript based Query window + <a class="reference internal" href="glossary.html#term-sql"><span class="xref std std-term">SQL</span></a> history</li>
<li>Improvement of column/db comments</li>
<li>(MIME)-Transformations for columns</li>
<li>Use custom alias names for Databases in left frame</li>
<li>hierarchical/nested table display</li>
<li><a class="reference internal" href="glossary.html#term-pdf"><span class="xref std std-term">PDF</span></a>-scratchboard for WYSIWYG-
distribution of <a class="reference internal" href="glossary.html#term-pdf"><span class="xref std std-term">PDF</span></a> relations</li>
<li>new icon sets</li>
<li>vertical display of column properties page</li>
<li>some bugfixes, features, support, German language additions</li>
</ul>
</li>
<li>Yukihiro Kawada &lt;kawada_at_den.fujifilm.co.jp&gt;<ul>
<li>japanese kanji encoding conversion feature</li>
</ul>
</li>
<li>Piotr Roszatycki &lt;d3xter_at_users.sourceforge.net&gt; and Dan Wilson<ul>
<li>the Cookie authentication mode</li>
</ul>
</li>
<li>Axel Sander &lt;n8falke_at_users.sourceforge.net&gt;<ul>
<li>table relation-links feature</li>
</ul>
</li>
<li>Maxime Delorme &lt;delorme.maxime_at_free.fr&gt;<ul>
<li><a class="reference internal" href="glossary.html#term-pdf"><span class="xref std std-term">PDF</span></a> schema output, thanks also to
Olivier Plathey for the &#8220;FPDF&#8221; library (see &lt;<a class="reference external" href="http://www.fpdf.org/">http://www.fpdf.org/</a>&gt;), Steven
Wittens for the &#8220;UFPDF&#8221; library and
Nicola Asuni for the &#8220;TCPDF&#8221; library (see &lt;<a class="reference external" href="https://tcpdf.org/">https://tcpdf.org/</a>&gt;).</li>
</ul>
</li>
<li>Olof Edlund &lt;olof.edlund_at_upright.se&gt;<ul>
<li><a class="reference internal" href="glossary.html#term-sql"><span class="xref std std-term">SQL</span></a> validator server</li>
</ul>
</li>
<li>Ivan R. Lanin &lt;ivanlanin_at_users.sourceforge.net&gt;<ul>
<li>phpMyAdmin logo (until June 2004)</li>
</ul>
</li>
<li>Mike Cochrane &lt;mike_at_graftonhall.co.nz&gt;<ul>
<li>blowfish library from the Horde project (withdrawn in release 4.0)</li>
</ul>
</li>
<li>Marcel Tschopp &lt;ne0x_at_users.sourceforge.net&gt;<ul>
<li>mysqli support</li>
<li>many bugfixes and improvements</li>
</ul>
</li>
<li>Nicola Asuni (Tecnick.com)<ul>
<li>TCPDF library (&lt;<a class="reference external" href="https://tcpdf.org">https://tcpdf.org</a>&gt;)</li>
</ul>
</li>
<li>Michael Keck &lt;mkkeck_at_users.sourceforge.net&gt;<ul>
<li>redesign for 2.6.0</li>
<li>phpMyAdmin sailboat logo (June 2004)</li>
</ul>
</li>
<li>Mathias Landhäußer<ul>
<li>Representation at conferences</li>
</ul>
</li>
<li>Sebastian Mendel &lt;cybot_tm_at_users.sourceforge.net&gt;<ul>
<li>interface improvements</li>
<li>various bugfixes</li>
</ul>
</li>
<li>Ivan A Kirillov<ul>
<li>new relations Designer</li>
</ul>
</li>
<li>Raj Kissu Rajandran (Google Summer of Code 2008)<ul>
<li>BLOBstreaming support (withdrawn in release 4.0)</li>
</ul>
</li>
<li>Piotr Przybylski (Google Summer of Code 2008, 2010 and 2011)<ul>
<li>improved setup script</li>
<li>user preferences</li>
<li>Drizzle support</li>
</ul>
</li>
<li>Derek Schaefer (Google Summer of Code 2009)<ul>
<li>Improved the import system</li>
</ul>
</li>
<li>Alexander Rutkowski (Google Summer of Code 2009)<ul>
<li>Tracking mechanism</li>
</ul>
</li>
<li>Zahra Naeem (Google Summer of Code 2009)<ul>
<li>Synchronization feature (removed in release 4.0)</li>
</ul>
</li>
<li>Tomáš Srnka (Google Summer of Code 2009)<ul>
<li>Replication support</li>
</ul>
</li>
<li>Muhammad Adnan (Google Summer of Code 2010)<ul>
<li>Relation schema export to multiple formats</li>
</ul>
</li>
<li>Lori Lee (Google Summer of Code 2010)<ul>
<li>User interface improvements</li>
<li>ENUM/SET editor</li>
<li>Simplified interface for export/import</li>
</ul>
</li>
<li>Ninad Pundalik (Google Summer of Code 2010)<ul>
<li>AJAXifying the interface</li>
</ul>
</li>
<li>Martynas Mickevičius (Google Summer of Code 2010)<ul>
<li>Charts</li>
</ul>
</li>
<li>Barrie Leslie<ul>
<li>BLOBstreaming support with PBMS PHP extension (withdrawn in release
4.0)</li>
</ul>
</li>
<li>Ankit Gupta (Google Summer of Code 2010)<ul>
<li>Visual query builder</li>
</ul>
</li>
<li>Madhura Jayaratne (Google Summer of Code 2011)<ul>
<li>OpenGIS support</li>
</ul>
</li>
<li>Ammar Yasir (Google Summer of Code 2011)<ul>
<li>Zoom search</li>
</ul>
</li>
<li>Aris Feryanto (Google Summer of Code 2011)<ul>
<li>Browse-mode improvements</li>
</ul>
</li>
<li>Thilanka Kaushalya (Google Summer of Code 2011)<ul>
<li>AJAXification</li>
</ul>
</li>
<li>Tyron Madlener (Google Summer of Code 2011)<ul>
<li>Query statistics and charts for the status page</li>
</ul>
</li>
<li>Zarubin Stas (Google Summer of Code 2011)<ul>
<li>Automated testing</li>
</ul>
</li>
<li>Rouslan Placella (Google Summer of Code 2011 and 2012)<ul>
<li>Improved support for Stored Routines, Triggers and Events</li>
<li>Italian translation updates</li>
<li>Removal of frames, new navigation</li>
</ul>
</li>
<li>Dieter Adriaenssens<ul>
<li>Various bugfixes</li>
<li>Dutch translation updates</li>
</ul>
</li>
<li>Alex Marin (Google Summer of Code 2012)<ul>
<li>New plugins and properties system</li>
</ul>
</li>
<li>Thilina Buddika Abeyrathna (Google Summer of Code 2012)<ul>
<li>Refactoring</li>
</ul>
</li>
<li>Atul Pratap Singh  (Google Summer of Code 2012)<ul>
<li>Refactoring</li>
</ul>
</li>
<li>Chanaka Indrajith (Google Summer of Code 2012)<ul>
<li>Refactoring</li>
</ul>
</li>
<li>Yasitha Pandithawatta (Google Summer of Code 2012)<ul>
<li>Automated testing</li>
</ul>
</li>
<li>Jim Wigginton (phpseclib.sourceforge.net)<ul>
<li>phpseclib</li>
</ul>
</li>
<li>Bin Zu (Google Summer of Code 2013)<ul>
<li>Refactoring</li>
</ul>
</li>
<li>Supun Nakandala (Google Summer of Code 2013)<ul>
<li>Refactoring</li>
</ul>
</li>
<li>Mohamed Ashraf (Google Summer of Code 2013)<ul>
<li>AJAX error reporting</li>
</ul>
</li>
<li>Adam Kang (Google Summer of Code 2013)<ul>
<li>Automated testing</li>
</ul>
</li>
<li>Ayush Chaudhary (Google Summer of Code 2013)<ul>
<li>Automated testing</li>
</ul>
</li>
<li>Kasun Chathuranga (Google Summer of Code 2013)<ul>
<li>Interface improvements</li>
</ul>
</li>
<li>Hugues Peccatte<ul>
<li>Load/save query by example (database search bookmarks)</li>
</ul>
</li>
<li>Smita Kumari (Google Summer of Code 2014)<ul>
<li>Central list of columns</li>
<li>Improve table structure (normalization)</li>
</ul>
</li>
<li>Ashutosh Dhundhara (Google Summer of Code 2014)<ul>
<li>Interface improvements</li>
</ul>
</li>
<li>Dhananjay Nakrani (Google Summer of Code 2014)<ul>
<li>PHP error reporting</li>
</ul>
</li>
<li>Edward Cheng (Google Summer of Code 2014)<ul>
<li>SQL Query Console</li>
</ul>
</li>
<li>Kankanamge Bimal Yashodha (Google Summer of Code 2014)<ul>
<li>Refactoring: Designer/schema integration</li>
</ul>
</li>
<li>Chirayu Chiripal (Google Summer of Code 2014)<ul>
<li>Custom field handlers (Input based MIME transformations)</li>
<li>Export with table/column name changes</li>
</ul>
</li>
<li>Dan Ungureanu (Google Summer of Code 2015)<ul>
<li>New parser and analyzer</li>
</ul>
</li>
<li>Nisarg Jhaveri (Google Summer of Code 2015)<ul>
<li>Page-related settings</li>
<li>SQL debugging integration to the Console</li>
<li>Other UI improvements</li>
</ul>
</li>
<li>Deven Bansod (Google Summer of Code 2015)<ul>
<li>Print view using CSS</li>
<li>Other UI improvements and new features</li>
</ul>
</li>
<li>Deven Bansod (Google Summer of Code 2017)<ul>
<li>Improvements to the Error Reporting Server</li>
<li>Improved Selenium testing</li>
</ul>
</li>
<li>Manish Bisht (Google Summer of Code 2017)<ul>
<li>Mobile user interface</li>
<li>Remove inline JavaScript code</li>
<li>Other UI improvements</li>
</ul>
</li>
<li>Raghuram Vadapalli (Google Summer of Code 2017)<ul>
<li>Multi-table query interface</li>
<li>Allow Designer to work with tables from other databases</li>
<li>Other UI improvements</li>
</ul>
</li>
</ul>
<p>And also to the following people who have contributed minor changes,
enhancements, bugfixes or support for a new language since version
2.1.0:</p>
<p>Bora Alioglu, Ricardo ?, Sven-Erik Andersen, Alessandro Astarita,
Péter Bakondy, Borges Botelho, Olivier Bussier, Neil Darlow, Mats
Engstrom, Ian Davidson, Laurent Dhima, Kristof Hamann, Thomas Kläger,
Lubos Klokner, Martin Marconcini, Girish Nair, David Nordenberg,
Andreas Pauley, Bernard M. Piller, Laurent Haas, &#8220;Sakamoto&#8221;, Yuval
Sarna, www.securereality.com.au, Alexis Soulard, Alvar Soome, Siu Sun,
Peter Svec, Michael Tacelosky, Rachim Tamsjadi, Kositer Uros, Luís V.,
Martijn W. van der Lee, Algis Vainauskas, Daniel Villanueva, Vinay,
Ignacio Vazquez-Abrams, Chee Wai, Jakub Wilk, Thomas Michael
Winningham, Vilius Zigmantas, &#8220;Manuzhai&#8221;.</p>
</div>
<div class="section" id="translators">
<h2>Translators<a class="headerlink" href="#translators" title="Permalink to this headline">¶</a></h2>
<p>Following people have contributed to translation of phpMyAdmin:</p>
<ul>
<li><p class="first">Albanian</p>
<blockquote>
<div><ul class="simple">
<li>Arben Çokaj &lt;acokaj_at_shkoder.net&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Arabic</p>
<blockquote>
<div><ul class="simple">
<li>Ahmed Saleh Abd El-Raouf Ismae &lt;a.saleh.ismael_at_gmail.com&gt;</li>
<li>Ahmed Saad &lt;egbrave_at_hotmail.com&gt;</li>
<li>hassan mokhtari &lt;persiste1_at_gmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Armenian</p>
<blockquote>
<div><ul class="simple">
<li>Andrey Aleksanyants &lt;aaleksanyants_at_yahoo.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Azerbaijani</p>
<blockquote>
<div><ul class="simple">
<li>Mircəlal &lt;01youknowme_at_gmail.com&gt;</li>
<li>Huseyn &lt;huseyn_esgerov_at_mail.ru&gt;</li>
<li>Sevdimali İsa &lt;sevdimaliisayev_at_mail.ru&gt;</li>
<li>Jafar &lt;sharifov_at_programmer.net&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Belarusian</p>
<blockquote>
<div><ul class="simple">
<li>Viktar Palstsiuk &lt;vipals_at_gmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Bulgarian</p>
<blockquote>
<div><ul class="simple">
<li>Boyan Kehayov &lt;bkehayov_at_gmail.com&gt;</li>
<li>Valter Georgiev &lt;blagynchy_at_gmail.com&gt;</li>
<li>Valentin Mladenov &lt;hudsonvsm_at_gmail.com&gt;</li>
<li>P &lt;plamen_mbx_at_yahoo.com&gt;</li>
<li>krasimir &lt;vip_at_krasio-valia.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Catalan</p>
<blockquote>
<div><ul class="simple">
<li>josep constanti &lt;jconstanti_at_yahoo.es&gt;</li>
<li>Xavier Navarro &lt;xvnavarro_at_gmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Chinese (China)</p>
<blockquote>
<div><ul class="simple">
<li>Vincent Lau &lt;3092849_at_qq.com&gt;</li>
<li>Zheng Dan &lt;clanboy_at_163.com&gt;</li>
<li>disorderman &lt;disorderman_at_qq.com&gt;</li>
<li>Rex Lee &lt;duguying2008_at_gmail.com&gt;</li>
<li>&lt;fundawang_at_gmail.com&gt;</li>
<li>popcorner &lt;memoword_at_163.com&gt;</li>
<li>Yizhou Qiang &lt;qyz.yswy_at_hotmail.com&gt;</li>
<li>zz &lt;tczzjin_at_gmail.com&gt;</li>
<li>Terry Weng &lt;wengshiyu_at_gmail.com&gt;</li>
<li>whh &lt;whhlcj_at_126.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Chinese (Taiwan)</p>
<blockquote>
<div><ul class="simple">
<li>Albert Song &lt;albb0920_at_gmail.com&gt;</li>
<li>Chien Wei Lin &lt;cwlin0416_at_gmail.com&gt;</li>
<li>Peter Dave Hello &lt;xs910203_at_gmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Colognian</p>
<blockquote>
<div><ul class="simple">
<li>Purodha &lt;publi_at_web.de&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Czech</p>
<blockquote>
<div><ul class="simple">
<li>Aleš Hakl &lt;ales_at_hakl.net&gt;</li>
<li>Dalibor Straka &lt;dalibor.straka3_at_gmail.com&gt;</li>
<li>Martin Vidner &lt;martin_at_vidner.net&gt;</li>
<li>Ondra Šimeček &lt;ondrasek.simecek_at_gmail.com&gt;</li>
<li>Jan Palider &lt;palider_at_seznam.cz&gt;</li>
<li>Petr Kateřiňák &lt;petr.katerinak_at_gmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Danish</p>
<blockquote>
<div><ul class="simple">
<li>Aputsiaĸ Niels Janussen &lt;aj_at_isit.gl&gt;</li>
<li>Dennis Jakobsen &lt;dennis.jakobsen_at_gmail.com&gt;</li>
<li>Jonas &lt;jonas.den.smarte_at_gmail.com&gt;</li>
<li>Claus Svalekjaer &lt;just.my.smtp.server_at_gmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Dutch</p>
<blockquote>
<div><ul class="simple">
<li><ol class="first upperalpha">
<li>Voogt &lt;a.voogt_at_hccnet.nl&gt;</li>
</ol>
</li>
<li>dingo thirteen &lt;dingo13_at_gmail.com&gt;</li>
<li>Robin van der Vliet &lt;info_at_robinvandervliet.nl&gt;</li>
<li>Dieter Adriaenssens &lt;ruleant_at_users.sourceforge.net&gt;</li>
<li>Niko Strijbol &lt;strijbol.niko_at_gmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">English (United Kingdom)</p>
<blockquote>
<div><ul class="simple">
<li>Dries Verschuere &lt;dries.verschuere_at_outlook.com&gt;</li>
<li>Francisco Rocha &lt;j.francisco.o.rocha_at_zoho.com&gt;</li>
<li>Marc Delisle &lt;marc_at_infomarc.info&gt;</li>
<li>Marek Tomaštík &lt;tomastik.m_at_gmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Esperanto</p>
<blockquote>
<div><ul class="simple">
<li>Eliovir &lt;eliovir_at_gmail.com&gt;</li>
<li>Robin van der Vliet &lt;info_at_robinvandervliet.nl&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Estonian</p>
<blockquote>
<div><ul class="simple">
<li>Kristjan Räts &lt;kristjanrats_at_gmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Finnish</p>
<blockquote>
<div><ul class="simple">
<li>Juha Remes &lt;jremes_at_outlook.com&gt;</li>
<li>Lari Oesch &lt;lari_at_oesch.me&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">French</p>
<blockquote>
<div><ul class="simple">
<li>Marc Delisle &lt;marc_at_infomarc.info&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Frisian</p>
<blockquote>
<div><ul class="simple">
<li>Robin van der Vliet &lt;info_at_robinvandervliet.nl&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Galician</p>
<blockquote>
<div><ul class="simple">
<li>Xosé Calvo &lt;xosecalvo_at_gmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">German</p>
<blockquote>
<div><ul class="simple">
<li>Julian Ladisch &lt;github.com-t3if_at_ladisch.de&gt;</li>
<li>Jan Erik Zassenhaus &lt;jan.zassenhaus_at_jgerman.de&gt;</li>
<li>Lasse Goericke &lt;lasse_at_mydom.de&gt;</li>
<li>Matthias Bluthardt &lt;matthias_at_bluthardt.org&gt;</li>
<li>Michael Koch &lt;michael.koch_at_enough.de&gt;</li>
<li>Ann + J.M. &lt;phpMyAdmin_at_ZweiSteinSoft.de&gt;</li>
<li>&lt;pma_at_sebastianmendel.de&gt;</li>
<li>Phillip Rohmberger &lt;rohmberger_at_hotmail.de&gt;</li>
<li>Hauke Henningsen &lt;sqrt_at_entless.org&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Greek</p>
<blockquote>
<div><ul class="simple">
<li>Παναγιώτης Παπάζογλου &lt;papaz_p_at_yahoo.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Hebrew</p>
<blockquote>
<div><ul class="simple">
<li>Moshe Harush &lt;mmh15_at_windowslive.com&gt;</li>
<li>Yaron Shahrabani &lt;sh.yaron_at_gmail.com&gt;</li>
<li>Eyal Visoker &lt;visokereyal_at_gmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Hindi</p>
<blockquote>
<div><ul class="simple">
<li>Atul Pratap Singh &lt;atulpratapsingh05_at_gmail.com&gt;</li>
<li>Yogeshwar &lt;charanyogeshwar_at_gmail.com&gt;</li>
<li>Deven Bansod &lt;devenbansod.bits_at_gmail.com&gt;</li>
<li>Kushagra Pandey &lt;kushagra4296_at_gmail.com&gt;</li>
<li>Nisarg Jhaveri &lt;nisargjhaveri_at_gmail.com&gt;</li>
<li>Roohan Kazi &lt;roohan_cena_at_yahoo.co.in&gt;</li>
<li>Yugal Pantola &lt;yug.scorpio_at_gmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Hungarian</p>
<blockquote>
<div><ul class="simple">
<li>Akos Eros &lt;erosakos02_at_gmail.com&gt;</li>
<li>Dániel Tóth &lt;leedermeister_at_gmail.com&gt;</li>
<li>Szász Attila &lt;undernetangel_at_gmail.com&gt;</li>
<li>Balázs Úr &lt;urbalazs_at_gmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Indonesian</p>
<blockquote>
<div><ul class="simple">
<li>Deky Arifianto &lt;Deky40_at_gmail.com&gt;</li>
<li>Andika Triwidada &lt;andika_at_gmail.com&gt;</li>
<li>Dadan Setia &lt;da2n_s_at_yahoo.co.id&gt;</li>
<li>Dadan Setia &lt;dadan.setia_at_gmail.com&gt;</li>
<li>Yohanes Edwin &lt;edwin_at_yohanesedwin.com&gt;</li>
<li>Fadhiil Rachman &lt;fadhiilrachman_at_gmail.com&gt;</li>
<li>Benny &lt;tarzq28_at_gmail.com&gt;</li>
<li>Tommy Surbakti &lt;tommy_at_surbakti.net&gt;</li>
<li>Zufar Fathi Suhardi &lt;zufar.bogor_at_gmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Interlingua</p>
<blockquote>
<div><ul class="simple">
<li>Giovanni Sora &lt;g.sora_at_tiscali.it&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Italian</p>
<blockquote>
<div><ul class="simple">
<li>Francesco Saverio Giacobazzi &lt;francesco.giacobazzi_at_ferrania.it&gt;</li>
<li>Marco Pozzato &lt;ironpotts_at_gmail.com&gt;</li>
<li>Stefano Martinelli &lt;stefano.ste.martinelli_at_gmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Japanese</p>
<blockquote>
<div><ul class="simple">
<li>k725 &lt;alexalex.kobayashi_at_gmail.com&gt;</li>
<li>Hiroshi Chiyokawa &lt;hiroshi.chiyokawa_at_gmail.com&gt;</li>
<li>Masahiko HISAKAWA &lt;orzkun_at_ageage.jp&gt;</li>
<li>worldwideskier &lt;worldwideskier_at_yahoo.co.jp&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Kannada</p>
<blockquote>
<div><ul class="simple">
<li>Robin van der Vliet &lt;info_at_robinvandervliet.nl&gt;</li>
<li>Shameem Ahmed A Mulla &lt;shameem.sam_at_gmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Korean</p>
<blockquote>
<div><ul class="simple">
<li>Bumsoo Kim &lt;bskim45_at_gmail.com&gt;</li>
<li>Kyeong Su Shin &lt;cdac1234_at_gmail.com&gt;</li>
<li>Dongyoung Kim &lt;dckyoung_at_gmail.com&gt;</li>
<li>Myung-han Yu &lt;greatymh_at_gmail.com&gt;</li>
<li>JongDeok &lt;human.zion_at_gmail.com&gt;</li>
<li>Yong Kim &lt;kim_at_nhn.com&gt;</li>
<li>이경준 &lt;kyungjun2_at_gmail.com&gt;</li>
<li>Seongki Shin &lt;skshin_at_gmail.com&gt;</li>
<li>Yoon Bum-Jong &lt;virusyoon_at_gmail.com&gt;</li>
<li>Koo Youngmin &lt;youngminz.kr_at_gmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Kurdish Sorani</p>
<blockquote>
<div><ul class="simple">
<li>Alan Hilal &lt;alan.hilal94_at_gmail.com&gt;</li>
<li>Aso Naderi &lt;aso.naderi_at_gmail.com&gt;</li>
<li>muhammad &lt;esy_vb_at_yahoo.com&gt;</li>
<li>Zrng Abdulla &lt;zhyarabdulla94_at_gmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Latvian</p>
<blockquote>
<div><ul class="simple">
<li>Latvian TV &lt;dnighttv_at_gmail.com&gt;</li>
<li>Edgars Neimanis &lt;edgarsneims5092_at_inbox.lv&gt;</li>
<li>Ukko &lt;perkontevs_at_gmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Limburgish</p>
<blockquote>
<div><ul class="simple">
<li>Robin van der Vliet &lt;info_at_robinvandervliet.nl&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Lithuanian</p>
<blockquote>
<div><ul class="simple">
<li>Vytautas Motuzas &lt;v.motuzas_at_gmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Malay</p>
<blockquote>
<div><ul class="simple">
<li>Amir Hamzah &lt;amir.overlord666_at_gmail.com&gt;</li>
<li>diprofinfiniti &lt;anonynuine-999_at_yahoo.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Nepali</p>
<blockquote>
<div><ul class="simple">
<li>Nabin Ghimire &lt;nnabinn_at_hotmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Norwegian Bokmål</p>
<blockquote>
<div><ul class="simple">
<li>Børge Holm-Wennberg &lt;borge947_at_gmail.com&gt;</li>
<li>Tor Stokkan &lt;danorse_at_gmail.com&gt;</li>
<li>Espen Frøyshov &lt;efroys_at_gmail.com&gt;</li>
<li>Kurt Eilertsen &lt;kurt_at_kheds.com&gt;</li>
<li>Christoffer Haugom &lt;ph3n1x.nobody_at_gmail.com&gt;</li>
<li>Sebastian &lt;sebastian_at_sgundersen.com&gt;</li>
<li>Tomas &lt;tomas_at_tomasruud.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Persian</p>
<blockquote>
<div><ul class="simple">
<li>ashkan shirian &lt;ashkan.shirian_at_gmail.com&gt;</li>
<li>HM &lt;goodlinuxuser_at_chmail.ir&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Polish</p>
<blockquote>
<div><ul class="simple">
<li>Andrzej &lt;andrzej_at_kynu.pl&gt;</li>
<li>Przemo &lt;info_at_opsbielany.waw.pl&gt;</li>
<li>Krystian Biesaga &lt;krystian4842_at_gmail.com&gt;</li>
<li>Maciej Gryniuk &lt;maciejka45_at_gmail.com&gt;</li>
<li>Michał VonFlynee &lt;vonflynee_at_gmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Portuguese</p>
<blockquote>
<div><ul class="simple">
<li>Alexandre Badalo &lt;alexandre.badalo_at_sapo.pt&gt;</li>
<li>João Rodrigues &lt;geral_at_jonilive.com&gt;</li>
<li>Pedro Ribeiro &lt;p.m42.ribeiro_at_gmail.com&gt;</li>
<li>Sandro Amaral &lt;sandro123iv_at_gmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Portuguese (Brazil)</p>
<blockquote>
<div><ul class="simple">
<li>Alex Rohleder &lt;alexrohleder96_at_outlook.com&gt;</li>
<li>bruno mendax &lt;brunomendax_at_gmail.com&gt;</li>
<li>Danilo GUia &lt;danilo.eng_at_globomail.com&gt;</li>
<li>Douglas Rafael Morais Kollar &lt;douglas.kollar_at_pg.df.gov.br&gt;</li>
<li>Douglas Eccker &lt;douglaseccker_at_hotmail.com&gt;</li>
<li>Ed Jr &lt;edjacobjunior_at_gmail.com&gt;</li>
<li>Guilherme Souza Silva &lt;g.szsilva_at_gmail.com&gt;</li>
<li>Guilherme Seibt &lt;gui_at_webseibt.net&gt;</li>
<li>Helder Santana &lt;helder.bs.santana_at_gmail.com&gt;</li>
<li>Junior Zancan &lt;jrzancan_at_hotmail.com&gt;</li>
<li>Luis &lt;luis.eduardo.braschi_at_outlook.com&gt;</li>
<li>Marcos Algeri &lt;malgeri_at_gmail.com&gt;</li>
<li>Marc Delisle &lt;marc_at_infomarc.info&gt;</li>
<li>Renato Rodrigues de Lima Júnio &lt;renatomdd_at_yahoo.com.br&gt;</li>
<li>Thiago Casotti &lt;thiago.casotti_at_uol.com.br&gt;</li>
<li>Victor Laureano &lt;victor.laureano_at_gmail.com&gt;</li>
<li>Vinícius Araújo &lt;vinipitta_at_gmail.com&gt;</li>
<li>Washington Bruno Rodrigues Cav &lt;washingtonbruno_at_msn.com&gt;</li>
<li>Yan Gabriel &lt;yansilvagabriel_at_gmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Punjabi</p>
<blockquote>
<div><ul class="simple">
<li>Robin van der Vliet &lt;info_at_robinvandervliet.nl&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Romanian</p>
<blockquote>
<div><ul class="simple">
<li>Alex &lt;amihaita_at_yahoo.com&gt;</li>
<li>Costel Cocerhan &lt;costa1988sv_at_gmail.com&gt;</li>
<li>Ion Adrian-Ionut &lt;john_at_panevo.ro&gt;</li>
<li>Raul Molnar &lt;molnar.raul_at_wservices.eu&gt;</li>
<li>Deleted User &lt;noreply_at_weblate.org&gt;</li>
<li>Stefan Murariu &lt;stefan.murariu_at_yahoo.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Russian</p>
<blockquote>
<div><ul class="simple">
<li>Andrey Aleksanyants &lt;aaleksanyants_at_yahoo.com&gt;</li>
<li>&lt;ddrmoscow_at_gmail.com&gt;</li>
<li>Robin van der Vliet &lt;info_at_robinvandervliet.nl&gt;</li>
<li>Хомутов Иван Сергеевич &lt;khomutov.ivan_at_mail.ru&gt;</li>
<li>Alexey Rubinov &lt;orion1979_at_yandex.ru&gt;</li>
<li>Олег Карпов &lt;salvadoporjc_at_gmail.com&gt;</li>
<li>Egorov Artyom &lt;unlucky_at_inbox.ru&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Serbian</p>
<blockquote>
<div><ul class="simple">
<li>Smart Kid &lt;kidsmart33_at_gmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Sinhala</p>
<blockquote>
<div><ul class="simple">
<li>Madhura Jayaratne &lt;madhura.cj_at_gmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Slovak</p>
<blockquote>
<div><ul class="simple">
<li>Martin Lacina &lt;martin_at_whistler.sk&gt;</li>
<li>Patrik Kollmann &lt;parkourpotex_at_gmail.com&gt;</li>
<li>Jozef Pistej &lt;pistej2_at_gmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Slovenian</p>
<blockquote>
<div><ul class="simple">
<li>Domen &lt;mitenem_at_outlook.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Spanish</p>
<blockquote>
<div><ul class="simple">
<li>Luis García Sevillano &lt;floss.dev_at_gmail.com&gt;</li>
<li>Franco &lt;fulanodetal.github1_at_openaliasbox.org&gt;</li>
<li>Luis Ruiz &lt;luisan00_at_hotmail.com&gt;</li>
<li>Macofe &lt;macofe.languagetool_at_gmail.com&gt;</li>
<li>Matías Bellone &lt;matiasbellone+weblate_at_gmail.com&gt;</li>
<li>Rodrigo A. &lt;ra4_at_openmailbox.org&gt;</li>
<li>FAMMA TV NOTICIAS MEDIOS DE CO &lt;revistafammatvmusic.oficial_at_gmail.com&gt;</li>
<li>Ronnie Simon &lt;ronniesimonf_at_gmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Swedish</p>
<blockquote>
<div><ul class="simple">
<li>Anders Jonsson &lt;anders.jonsson_at_norsjovallen.se&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Tamil</p>
<blockquote>
<div><ul class="simple">
<li>கணேஷ் குமார் &lt;GANESHTHEONE_at_gmail.com&gt;</li>
<li>Achchuthan Yogarajah &lt;achch1990_at_gmail.com&gt;</li>
<li>Rifthy Ahmed &lt;rifthy456_at_gmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Thai</p>
<blockquote>
<div><ul class="simple">
<li>&lt;nontawat39_at_gmail.com&gt;</li>
<li>Somthanat W. &lt;somthanat_at_gmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Turkish</p>
<blockquote>
<div><ul class="simple">
<li>Burak Yavuz &lt;hitowerdigit_at_hotmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Ukrainian</p>
<blockquote>
<div><ul class="simple">
<li>Сергій Педько &lt;nitrotoll_at_gmail.com&gt;</li>
<li>Igor &lt;vmta_at_yahoo.com&gt;</li>
<li>Vitaliy Perekupka &lt;vperekupka_at_gmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Vietnamese</p>
<blockquote>
<div><ul class="simple">
<li>Bao Phan &lt;baophan94_at_icloud.com&gt;</li>
<li>Xuan Hung &lt;mr.hungdx_at_gmail.com&gt;</li>
<li>Bao trinh minh &lt;trinhminhbao_at_gmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">West Flemish</p>
<blockquote>
<div><ul class="simple">
<li>Robin van der Vliet &lt;info_at_robinvandervliet.nl&gt;</li>
</ul>
</div></blockquote>
</li>
</ul>
</div>
<div class="section" id="documentation-translators">
<h2>Documentation translators<a class="headerlink" href="#documentation-translators" title="Permalink to this headline">¶</a></h2>
<p>Following people have contributed to translation of phpMyAdmin documentation:</p>
<ul>
<li><p class="first">Albanian</p>
<blockquote>
<div><ul class="simple">
<li>Arben Çokaj &lt;acokaj_at_shkoder.net&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Arabic</p>
<blockquote>
<div><ul class="simple">
<li>Ahmed El Azzabi &lt;ahmedtek1993_at_gmail.com&gt;</li>
<li>Omar Essam &lt;omar_2412_at_live.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Armenian</p>
<blockquote>
<div><ul class="simple">
<li>Andrey Aleksanyants &lt;aaleksanyants_at_yahoo.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Azerbaijani</p>
<blockquote>
<div><ul class="simple">
<li>Mircəlal &lt;01youknowme_at_gmail.com&gt;</li>
<li>Sevdimali İsa &lt;sevdimaliisayev_at_mail.ru&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Catalan</p>
<blockquote>
<div><ul class="simple">
<li>josep constanti &lt;jconstanti_at_yahoo.es&gt;</li>
<li>Joan Montané &lt;joan_at_montane.cat&gt;</li>
<li>Xavier Navarro &lt;xvnavarro_at_gmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Chinese (China)</p>
<blockquote>
<div><ul class="simple">
<li>Vincent Lau &lt;3092849_at_qq.com&gt;</li>
<li>罗攀登 &lt;6375lpd_at_gmail.com&gt;</li>
<li>disorderman &lt;disorderman_at_qq.com&gt;</li>
<li>ITXiaoPang &lt;djh1017555_at_126.com&gt;</li>
<li>tunnel213 &lt;tunnel213_at_aliyun.com&gt;</li>
<li>Terry Weng &lt;wengshiyu_at_gmail.com&gt;</li>
<li>whh &lt;whhlcj_at_126.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Chinese (Taiwan)</p>
<blockquote>
<div><ul class="simple">
<li>Chien Wei Lin &lt;cwlin0416_at_gmail.com&gt;</li>
<li>Peter Dave Hello &lt;xs910203_at_gmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Czech</p>
<blockquote>
<div><ul class="simple">
<li>Aleš Hakl &lt;ales_at_hakl.net&gt;</li>
<li>Michal Čihař &lt;michal_at_cihar.com&gt;</li>
<li>Jan Palider &lt;palider_at_seznam.cz&gt;</li>
<li>Petr Kateřiňák &lt;petr.katerinak_at_gmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Danish</p>
<blockquote>
<div><ul class="simple">
<li>Aputsiaĸ Niels Janussen &lt;aj_at_isit.gl&gt;</li>
<li>Claus Svalekjaer &lt;just.my.smtp.server_at_gmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Dutch</p>
<blockquote>
<div><ul class="simple">
<li><ol class="first upperalpha">
<li>Voogt &lt;a.voogt_at_hccnet.nl&gt;</li>
</ol>
</li>
<li>dingo thirteen &lt;dingo13_at_gmail.com&gt;</li>
<li>Dries Verschuere &lt;dries.verschuere_at_outlook.com&gt;</li>
<li>Robin van der Vliet &lt;info_at_robinvandervliet.nl&gt;</li>
<li>Stefan Koolen &lt;nast3zz_at_gmail.com&gt;</li>
<li>Ray Borggreve &lt;ray_at_datahuis.net&gt;</li>
<li>Dieter Adriaenssens &lt;ruleant_at_users.sourceforge.net&gt;</li>
<li>Tom Hofman &lt;tom.hofman_at_gmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Estonian</p>
<blockquote>
<div><ul class="simple">
<li>Kristjan Räts &lt;kristjanrats_at_gmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Finnish</p>
<blockquote>
<div><ul class="simple">
<li>Juha &lt;jremes_at_outlook.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">French</p>
<blockquote>
<div><ul class="simple">
<li>Cédric Corazza &lt;cedric.corazza_at_wanadoo.fr&gt;</li>
<li>Étienne Gilli &lt;etienne.gilli_at_gmail.com&gt;</li>
<li>Marc Delisle &lt;marc_at_infomarc.info&gt;</li>
<li>Donavan_Martin &lt;mart.donavan_at_hotmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Frisian</p>
<blockquote>
<div><ul class="simple">
<li>Robin van der Vliet &lt;info_at_robinvandervliet.nl&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Galician</p>
<blockquote>
<div><ul class="simple">
<li>Xosé Calvo &lt;xosecalvo_at_gmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">German</p>
<blockquote>
<div><ul class="simple">
<li>Daniel &lt;d.gnauk89_at_googlemail.com&gt;</li>
<li>JH M &lt;janhenrikm_at_yahoo.de&gt;</li>
<li>Lasse Goericke &lt;lasse_at_mydom.de&gt;</li>
<li>Michael Koch &lt;michael.koch_at_enough.de&gt;</li>
<li>Ann + J.M. &lt;phpMyAdmin_at_ZweiSteinSoft.de&gt;</li>
<li>Niemand Jedermann &lt;predatorix_at_web.de&gt;</li>
<li>Phillip Rohmberger &lt;rohmberger_at_hotmail.de&gt;</li>
<li>Hauke Henningsen &lt;sqrt_at_entless.org&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Greek</p>
<blockquote>
<div><ul class="simple">
<li>Παναγιώτης Παπάζογλου &lt;papaz_p_at_yahoo.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Hungarian</p>
<blockquote>
<div><ul class="simple">
<li>Balázs Úr &lt;urbalazs_at_gmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Italian</p>
<blockquote>
<div><ul class="simple">
<li>Francesco Saverio Giacobazzi &lt;francesco.giacobazzi_at_ferrania.it&gt;</li>
<li>Marco Pozzato &lt;ironpotts_at_gmail.com&gt;</li>
<li>Stefano Martinelli &lt;stefano.ste.martinelli_at_gmail.com&gt;</li>
<li>TWS &lt;tablettws_at_gmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Japanese</p>
<blockquote>
<div><ul class="simple">
<li>Eshin Kunishima &lt;ek_at_luna.miko.im&gt;</li>
<li>Hiroshi Chiyokawa &lt;hiroshi.chiyokawa_at_gmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Lithuanian</p>
<blockquote>
<div><ul class="simple">
<li>Jur Kis &lt;atvejis_at_gmail.com&gt;</li>
<li>Dovydas &lt;dovy.buz_at_gmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Norwegian Bokmål</p>
<blockquote>
<div><ul class="simple">
<li>Tor Stokkan &lt;danorse_at_gmail.com&gt;</li>
<li>Kurt Eilertsen &lt;kurt_at_kheds.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Portuguese (Brazil)</p>
<blockquote>
<div><ul class="simple">
<li>Alexandre Moretti &lt;alemoretti2010_at_hotmail.com&gt;</li>
<li>Douglas Rafael Morais Kollar &lt;douglas.kollar_at_pg.df.gov.br&gt;</li>
<li>Guilherme Seibt &lt;gui_at_webseibt.net&gt;</li>
<li>Helder Santana &lt;helder.bs.santana_at_gmail.com&gt;</li>
<li>Michal Čihař &lt;michal_at_cihar.com&gt;</li>
<li>Michel Souza &lt;michel.ekio_at_gmail.com&gt;</li>
<li>Danilo Azevedo &lt;mrdaniloazevedo_at_gmail.com&gt;</li>
<li>Thiago Casotti &lt;thiago.casotti_at_uol.com.br&gt;</li>
<li>Vinícius Araújo &lt;vinipitta_at_gmail.com&gt;</li>
<li>Yan Gabriel &lt;yansilvagabriel_at_gmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Slovak</p>
<blockquote>
<div><ul class="simple">
<li>Martin Lacina &lt;martin_at_whistler.sk&gt;</li>
<li>Michal Čihař &lt;michal_at_cihar.com&gt;</li>
<li>Jozef Pistej &lt;pistej2_at_gmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Slovenian</p>
<blockquote>
<div><ul class="simple">
<li>Domen &lt;mitenem_at_outlook.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Spanish</p>
<blockquote>
<div><ul class="simple">
<li>Luis García Sevillano &lt;floss.dev_at_gmail.com&gt;</li>
<li>Franco &lt;fulanodetal.github1_at_openaliasbox.org&gt;</li>
<li>Matías Bellone &lt;matiasbellone+weblate_at_gmail.com&gt;</li>
<li>Ronnie Simon &lt;ronniesimonf_at_gmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">Turkish</p>
<blockquote>
<div><ul class="simple">
<li>Burak Yavuz &lt;hitowerdigit_at_hotmail.com&gt;</li>
</ul>
</div></blockquote>
</li>
</ul>
</div>
<div class="section" id="original-credits-of-version-2-1-0">
<h2>Original Credits of Version 2.1.0<a class="headerlink" href="#original-credits-of-version-2-1-0" title="Permalink to this headline">¶</a></h2>
<p>This work is based on Peter Kuppelwieser&#8217;s MySQL-Webadmin. It was his
idea to create a web-based interface to MySQL using PHP3. Although I
have not used any of his source-code, there are some concepts I&#8217;ve
borrowed from him. phpMyAdmin was created because Peter told me he
wasn&#8217;t going to further develop his (great) tool.</p>
<p>Thanks go to</p>
<ul class="simple">
<li>Amalesh Kempf &lt;ak-lsml_at_living-source.com&gt; who contributed the
code for the check when dropping a table or database. He also
suggested that you should be able to specify the primary key on
tbl_create.php3. To version 1.1.1 he contributed the ldi_*.php3-set
(Import text-files) as well as a bug-report. Plus many smaller
improvements.</li>
<li>Jan Legenhausen &lt;jan_at_nrw.net&gt;: He made many of the changes that
were introduced in 1.3.0 (including quite significant ones like the
authentication). For 1.4.1 he enhanced the table-dump feature. Plus
bug-fixes and help.</li>
<li>Marc Delisle &lt;DelislMa_at_CollegeSherbrooke.qc.ca&gt; made phpMyAdmin
language-independent by outsourcing the strings to a separate file. He
also contributed the French translation.</li>
<li>Alexandr Bravo &lt;abravo_at_hq.admiral.ru&gt; who contributed
tbl_select.php3, a feature to display only some columns from a table.</li>
<li>Chris Jackson &lt;chrisj_at_ctel.net&gt; added support for MySQL functions
in tbl_change.php3. He also added the &#8220;Query by Example&#8221; feature in
2.0.</li>
<li>Dave Walton &lt;walton_at_nordicdms.com&gt; added support for multiple
servers and is a regular contributor for bug-fixes.</li>
<li>Gabriel Ash &lt;ga244_at_is8.nyu.edu&gt; contributed the random access
features for 2.0.6.</li>
</ul>
<p>The following people have contributed minor changes, enhancements,
bugfixes or support for a new language:</p>
<p>Jim Kraai, Jordi Bruguera, Miquel Obrador, Geert Lund, Thomas
Kleemann, Alexander Leidinger, Kiko Albiol, Daniel C. Chao, Pavel
Piankov, Sascha Kettler, Joe Pruett, Renato Lins, Mark Kronsbein,
Jannis Hermanns, G. Wieggers.</p>
<p>And thanks to everyone else who sent me email with suggestions, bug-
reports and or just some feedback.</p>
</div>
</div>


          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <h3><a href="index.html">Table Of Contents</a></h3>
  <ul>
<li><a class="reference internal" href="#">Credits</a><ul>
<li><a class="reference internal" href="#credits-in-chronological-order">Credits, in chronological order</a></li>
<li><a class="reference internal" href="#translators">Translators</a></li>
<li><a class="reference internal" href="#documentation-translators">Documentation translators</a></li>
<li><a class="reference internal" href="#original-credits-of-version-2-1-0">Original Credits of Version 2.1.0</a></li>
</ul>
</li>
</ul>

  <h4>Previous topic</h4>
  <p class="topless"><a href="copyright.html"
                        title="previous chapter">Copyright</a></p>
  <h4>Next topic</h4>
  <p class="topless"><a href="glossary.html"
                        title="next chapter">Glossary</a></p>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="_sources/credits.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3>Quick search</h3>
    <form class="search" action="search.html" method="get">
      <div><input type="text" name="q" /></div>
      <div><input type="submit" value="Go" /></div>
      <input type="hidden" name="check_keywords" value="yes" />
      <input type="hidden" name="area" value="default" />
    </form>
</div>
<script type="text/javascript">$('#searchbox').show(0);</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="glossary.html" title="Glossary"
             >next</a> |</li>
        <li class="right" >
          <a href="copyright.html" title="Copyright"
             >previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">phpMyAdmin 4.8.5 documentation</a> &#187;</li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
        &#169; <a href="copyright.html">Copyright</a> 2012 - 2018, The phpMyAdmin devel team.
      Created using <a href="http://sphinx-doc.org/">Sphinx</a> 1.4.9.
    </div>
  </body>
</html>