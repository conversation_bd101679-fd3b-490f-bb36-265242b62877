<div id="layer_menu" class="hide">
    <div class="center">
        <a href="#" class="M_butt" target="_self" >
            <img title="{% trans 'Hide/Show all' %}"
                alt="v"
                id="key_HS_all"
                src="{{ theme.getImgPath('designer/downarrow1.png') }}"
                data-down="{{ theme.getImgPath('designer/downarrow1.png') }}"
                data-right="{{ theme.getImgPath('designer/rightarrow1.png') }}" />
        </a>
        <a href="#" class="M_butt" target="_self" >
            <img alt="v"
                id="key_HS"
                title="{% trans 'Hide/Show tables with no relationship' %}"
                src="{{ theme.getImgPath('designer/downarrow2.png') }}"
                data-down="{{ theme.getImgPath('designer/downarrow2.png') }}"
                data-right="{{ theme.getImgPath('designer/rightarrow2.png') }}" />
        </a>
    </div>
    <div id="id_scroll_tab" class="scroll_tab">
        <table width="100%" style="padding-left: 3px;">
            {% for i in 0..table_names|length - 1 %}
                <tr>
                    <td title="{% trans 'Structure' %}"
                        width="1px"
                        class="L_butt2_1">
                        <img alt=""
                            table_name="{{ table_names_small_url[i] }}"
                            class="scroll_tab_struct"
                            src="{{ theme.getImgPath('designer/exec.png') }}"/>
                    </td>
                    <td width="1px">
                        <input class="scroll_tab_checkbox"
                            title="{% trans 'Hide' %}"
                            id="check_vis_{{ table_names_url[i]|url_encode }}"
                            style="margin:0;"
                            type="checkbox"
                            value="{{ table_names_url[i]|url_encode }}"
                            {% if (tab_pos[table_names[i]] is defined
                                and tab_pos[table_names[i]]['H'])
                                or display_page == -1 -%}
                                checked="checked"
                            {%- endif %} />
                    </td>
                    <td class="designer_Tabs"
                        designer_url_table_name="{{ table_names_url[i]|url_encode }}">
                        {{ table_names_out[i]|raw }}
                    </td>
                </tr>
            {% endfor %}
        </table>
    </div>
    {# end id_scroll_tab #}
    <div class="center">
        {% trans 'Number of tables:' %} {{ table_names|length }}
    </div>
    <div id="layer_menu_sizer">
        <div class="floatleft">
            <img class="icon"
                data-right="{{ theme.getImgPath('designer/resizeright.png') }}"
                src="{{ theme.getImgPath('designer/resize.png') }}"/>
        </div>
    </div>
</div>
{# end layer_menu #}
