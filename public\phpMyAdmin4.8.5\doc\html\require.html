<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
  "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">


<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    
    <title>Requirements &#8212; phpMyAdmin 4.8.5 documentation</title>
    
    <link rel="stylesheet" href="_static/classic.css" type="text/css" />
    <link rel="stylesheet" href="_static/pygments.css" type="text/css" />
    
    <script type="text/javascript">
      var DOCUMENTATION_OPTIONS = {
        URL_ROOT:    './',
        VERSION:     '4.8.5',
        COLLAPSE_INDEX: false,
        FILE_SUFFIX: '.html',
        HAS_SOURCE:  true
      };
    </script>
    <script type="text/javascript" src="_static/jquery.js"></script>
    <script type="text/javascript" src="_static/underscore.js"></script>
    <script type="text/javascript" src="_static/doctools.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="copyright" title="Copyright" href="copyright.html" />
    <link rel="top" title="phpMyAdmin 4.8.5 documentation" href="index.html" />
    <link rel="next" title="Installation" href="setup.html" />
    <link rel="prev" title="Introduction" href="intro.html" /> 
  </head>
  <body role="document">
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="setup.html" title="Installation"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="intro.html" title="Introduction"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">phpMyAdmin 4.8.5 documentation</a> &#187;</li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <div class="section" id="requirements">
<span id="require"></span><h1>Requirements<a class="headerlink" href="#requirements" title="Permalink to this headline">¶</a></h1>
<div class="section" id="web-server">
<h2>Web server<a class="headerlink" href="#web-server" title="Permalink to this headline">¶</a></h2>
<p>Since phpMyAdmin&#8217;s interface is based entirely in your browser, you&#8217;ll need a
web server (such as Apache, nginx, <a class="reference internal" href="glossary.html#term-iis"><span class="xref std std-term">IIS</span></a>) to install phpMyAdmin&#8217;s files into.</p>
</div>
<div class="section" id="php">
<h2>PHP<a class="headerlink" href="#php" title="Permalink to this headline">¶</a></h2>
<ul class="simple">
<li>You need PHP 5.5.0 or newer, with <code class="docutils literal"><span class="pre">session</span></code> support, the Standard PHP Library
(SPL) extension, hash, ctype, and JSON support.</li>
<li>The <code class="docutils literal"><span class="pre">mbstring</span></code> extension (see <a class="reference internal" href="glossary.html#term-mbstring"><span class="xref std std-term">mbstring</span></a>) is strongly recommended
for performance reasons.</li>
<li>To support uploading of ZIP files, you need the PHP <code class="docutils literal"><span class="pre">zip</span></code> extension.</li>
<li>You need GD2 support in PHP to display inline thumbnails of JPEGs
(&#8220;image/jpeg: inline&#8221;) with their original aspect ratio.</li>
<li>When using the cookie authentication (the default), the <a class="reference external" href="https://secure.php.net/openssl">openssl</a> extension is strongly suggested.</li>
<li>To support upload progress bars, see <a class="reference internal" href="faq.html#faq2-9"><span class="std std-ref">2.9 Seeing an upload progress bar</span></a>.</li>
<li>To support XML and Open Document Spreadsheet importing, you need the
<a class="reference external" href="https://secure.php.net/libxml">libxml</a> extension.</li>
<li>To support reCAPTCHA on the login page, you need the
<a class="reference external" href="https://secure.php.net/openssl">openssl</a> extension.</li>
<li>To support displaying phpMyAdmin&#8217;s latest version, you need to enable
<code class="docutils literal"><span class="pre">allow_url_open</span></code> in your <code class="file docutils literal"><span class="pre">php.ini</span></code> or to have the
<a class="reference external" href="https://secure.php.net/curl">curl</a> extension.</li>
</ul>
<div class="admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last"><a class="reference internal" href="faq.html#faq1-31"><span class="std std-ref">1.31 Which PHP versions does phpMyAdmin support?</span></a>, <a class="reference internal" href="setup.html#authentication-modes"><span class="std std-ref">Using authentication modes</span></a></p>
</div>
</div>
<div class="section" id="database">
<h2>Database<a class="headerlink" href="#database" title="Permalink to this headline">¶</a></h2>
<p>phpMyAdmin supports MySQL-compatible databases.</p>
<ul class="simple">
<li>MySQL 5.5 or newer</li>
<li>MariaDB 5.5 or newer</li>
</ul>
<div class="admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last"><a class="reference internal" href="faq.html#faq1-17"><span class="std std-ref">1.17 Which Database versions does phpMyAdmin support?</span></a></p>
</div>
</div>
<div class="section" id="web-browser">
<h2>Web browser<a class="headerlink" href="#web-browser" title="Permalink to this headline">¶</a></h2>
<p>To access phpMyAdmin you need a web browser with cookies and JavaScript
enabled.</p>
<p>You need browser which is supported by jQuery 2.0, see
&lt;<a class="reference external" href="https://jquery.com/browser-support/">https://jquery.com/browser-support/</a>&gt;.</p>
</div>
</div>


          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <h3><a href="index.html">Table Of Contents</a></h3>
  <ul>
<li><a class="reference internal" href="#">Requirements</a><ul>
<li><a class="reference internal" href="#web-server">Web server</a></li>
<li><a class="reference internal" href="#php">PHP</a></li>
<li><a class="reference internal" href="#database">Database</a></li>
<li><a class="reference internal" href="#web-browser">Web browser</a></li>
</ul>
</li>
</ul>

  <h4>Previous topic</h4>
  <p class="topless"><a href="intro.html"
                        title="previous chapter">Introduction</a></p>
  <h4>Next topic</h4>
  <p class="topless"><a href="setup.html"
                        title="next chapter">Installation</a></p>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="_sources/require.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3>Quick search</h3>
    <form class="search" action="search.html" method="get">
      <div><input type="text" name="q" /></div>
      <div><input type="submit" value="Go" /></div>
      <input type="hidden" name="check_keywords" value="yes" />
      <input type="hidden" name="area" value="default" />
    </form>
</div>
<script type="text/javascript">$('#searchbox').show(0);</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="setup.html" title="Installation"
             >next</a> |</li>
        <li class="right" >
          <a href="intro.html" title="Introduction"
             >previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">phpMyAdmin 4.8.5 documentation</a> &#187;</li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
        &#169; <a href="copyright.html">Copyright</a> 2012 - 2018, The phpMyAdmin devel team.
      Created using <a href="http://sphinx-doc.org/">Sphinx</a> 1.4.9.
    </div>
  </body>
</html>