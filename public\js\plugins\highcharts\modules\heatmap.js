/*
 Highcharts JS v5.0.5 (2016-11-29)

 (c) 2009-2016 Torstein Honsi

 License: www.highcharts.com/license
*/
(function(n){"object"===typeof module&&module.exports?module.exports=n:n(Highcharts)})(function(n){(function(b){var g=b.Axis,r=b.Chart,m=b.color,k,e=b.each,v=b.extend,w=b.isNumber,p=b.Legend,h=b.LegendSymbolMixin,x=b.noop,q=b.merge,u=b.pick,t=b.wrap;k=b.ColorAxis=function(){this.init.apply(this,arguments)};v(k.prototype,g.prototype);v(k.prototype,{defaultColorAxisOptions:{lineWidth:0,minPadding:0,maxPadding:0,gridLineWidth:1,tickPixelInterval:72,startOnTick:!0,endOnTick:!0,offset:0,marker:{animation:{duration:50},
width:.01,color:"#999999"},labels:{overflow:"justify"},minColor:"#e6ebf5",maxColor:"#003399",tickLength:5,showInLegend:!0},keepProps:["legendGroup","legendItem","legendSymbol"].concat(g.prototype.keepProps),init:function(a,c){var d="vertical"!==a.options.legend.layout,f;this.coll="colorAxis";f=q(this.defaultColorAxisOptions,{side:d?2:1,reversed:!d},c,{opposite:!d,showEmpty:!1,title:null});g.prototype.init.call(this,a,f);c.dataClasses&&this.initDataClasses(c);this.initStops(c);this.horiz=d;this.zoomEnabled=
!1;this.defaultLegendLength=200},tweenColors:function(a,c,d){var f;c.rgba.length&&a.rgba.length?(a=a.rgba,c=c.rgba,f=1!==c[3]||1!==a[3],a=(f?"rgba(":"rgb(")+Math.round(c[0]+(a[0]-c[0])*(1-d))+","+Math.round(c[1]+(a[1]-c[1])*(1-d))+","+Math.round(c[2]+(a[2]-c[2])*(1-d))+(f?","+(c[3]+(a[3]-c[3])*(1-d)):"")+")"):a=c.input||"none";return a},initDataClasses:function(a){var c=this,d=this.chart,f,l=0,b=d.options.chart.colorCount,y=this.options,h=a.dataClasses.length;this.dataClasses=f=[];this.legendItems=
[];e(a.dataClasses,function(a,e){a=q(a);f.push(a);a.color||("category"===y.dataClassColor?(e=d.options.colors,b=e.length,a.color=e[l],a.colorIndex=l,l++,l===b&&(l=0)):a.color=c.tweenColors(m(y.minColor),m(y.maxColor),2>h?.5:e/(h-1)))})},initStops:function(a){this.stops=a.stops||[[0,this.options.minColor],[1,this.options.maxColor]];e(this.stops,function(a){a.color=m(a[1])})},setOptions:function(a){g.prototype.setOptions.call(this,a);this.options.crosshair=this.options.marker},setAxisSize:function(){var a=
this.legendSymbol,c=this.chart,d=c.options.legend||{},f,l;a?(this.left=d=a.attr("x"),this.top=f=a.attr("y"),this.width=l=a.attr("width"),this.height=a=a.attr("height"),this.right=c.chartWidth-d-l,this.bottom=c.chartHeight-f-a,this.len=this.horiz?l:a,this.pos=this.horiz?d:f):this.len=(this.horiz?d.symbolWidth:d.symbolHeight)||this.defaultLegendLength},toColor:function(a,c){var d=this.stops,f,l,b=this.dataClasses,h,e;if(b)for(e=b.length;e--;){if(h=b[e],f=h.from,d=h.to,(void 0===f||a>=f)&&(void 0===
d||a<=d)){l=h.color;c&&(c.dataClass=e,c.colorIndex=h.colorIndex);break}}else{this.isLog&&(a=this.val2lin(a));a=1-(this.max-a)/(this.max-this.min||1);for(e=d.length;e--&&!(a>d[e][0]););f=d[e]||d[e+1];d=d[e+1]||f;a=1-(d[0]-a)/(d[0]-f[0]||1);l=this.tweenColors(f.color,d.color,a)}return l},getOffset:function(){var a=this.legendGroup,c=this.chart.axisOffset[this.side];a&&(this.axisParent=a,g.prototype.getOffset.call(this),this.added||(this.added=!0,this.labelLeft=0,this.labelRight=this.width),this.chart.axisOffset[this.side]=
c)},setLegendColor:function(){var a,c=this.options,d=this.reversed;a=d?1:0;d=d?0:1;a=this.horiz?[a,0,d,0]:[0,d,0,a];this.legendColor={linearGradient:{x1:a[0],y1:a[1],x2:a[2],y2:a[3]},stops:c.stops||[[0,c.minColor],[1,c.maxColor]]}},drawLegendSymbol:function(a,c){var d=a.padding,f=a.options,l=this.horiz,b=u(f.symbolWidth,l?this.defaultLegendLength:12),e=u(f.symbolHeight,l?12:this.defaultLegendLength),h=u(f.labelPadding,l?16:30),f=u(f.itemDistance,10);this.setLegendColor();c.legendSymbol=this.chart.renderer.rect(0,
a.baseline-11,b,e).attr({zIndex:1}).add(c.legendGroup);this.legendItemWidth=b+d+(l?f:h);this.legendItemHeight=e+d+(l?h:0)},setState:x,visible:!0,setVisible:x,getSeriesExtremes:function(){var a;this.series.length&&(a=this.series[0],this.dataMin=a.valueMin,this.dataMax=a.valueMax)},drawCrosshair:function(a,c){var d=c&&c.plotX,f=c&&c.plotY,b,e=this.pos,h=this.len;c&&(b=this.toPixels(c[c.series.colorKey]),b<e?b=e-2:b>e+h&&(b=e+h+2),c.plotX=b,c.plotY=this.len-b,g.prototype.drawCrosshair.call(this,a,c),
c.plotX=d,c.plotY=f,this.cross&&(this.cross.addClass("highcharts-coloraxis-marker").add(this.legendGroup),this.cross.attr({fill:this.crosshair.color})))},getPlotLinePath:function(a,c,d,b,e){return w(e)?this.horiz?["M",e-4,this.top-6,"L",e+4,this.top-6,e,this.top,"Z"]:["M",this.left,e,"L",this.left-6,e+6,this.left-6,e-6,"Z"]:g.prototype.getPlotLinePath.call(this,a,c,d,b)},update:function(a,c){var d=this.chart,b=d.legend;e(this.series,function(a){a.isDirtyData=!0});a.dataClasses&&b.allItems&&(e(b.allItems,
function(a){a.isDataClass&&a.legendGroup.destroy()}),d.isDirtyLegend=!0);d.options[this.coll]=q(this.userOptions,a);g.prototype.update.call(this,a,c);this.legendItem&&(this.setLegendColor(),b.colorizeItem(this,!0))},getDataClassLegendSymbols:function(){var a=this,c=this.chart,d=this.legendItems,f=c.options.legend,l=f.valueDecimals,t=f.valueSuffix||"",g;d.length||e(this.dataClasses,function(f,p){var k=!0,q=f.from,m=f.to;g="";void 0===q?g="\x3c ":void 0===m&&(g="\x3e ");void 0!==q&&(g+=b.numberFormat(q,
l)+t);void 0!==q&&void 0!==m&&(g+=" - ");void 0!==m&&(g+=b.numberFormat(m,l)+t);d.push(v({chart:c,name:g,options:{},drawLegendSymbol:h.drawRectangle,visible:!0,setState:x,isDataClass:!0,setVisible:function(){k=this.visible=!k;e(a.series,function(a){e(a.points,function(a){a.dataClass===p&&a.setVisible(k)})});c.legend.colorizeItem(this,k)}},f))});return d},name:""});e(["fill","stroke"],function(a){b.Fx.prototype[a+"Setter"]=function(){this.elem.attr(a,k.prototype.tweenColors(m(this.start),m(this.end),
this.pos),null,!0)}});t(r.prototype,"getAxes",function(a){var c=this.options.colorAxis;a.call(this);this.colorAxis=[];c&&new k(this,c)});t(p.prototype,"getAllItems",function(a){var c=[],d=this.chart.colorAxis[0];d&&d.options&&(d.options.showInLegend&&(d.options.dataClasses?c=c.concat(d.getDataClassLegendSymbols()):c.push(d)),e(d.series,function(a){a.options.showInLegend=!1}));return c.concat(a.call(this))});t(p.prototype,"colorizeItem",function(a,c,d){a.call(this,c,d);d&&c.legendColor&&c.legendSymbol.attr({fill:c.legendColor})})})(n);
(function(b){var g=b.defined,r=b.each,m=b.noop,k=b.seriesTypes;b.colorPointMixin={isValid:function(){return null!==this.value},setVisible:function(b){var e=this,g=b?"show":"hide";r(["graphic","dataLabel"],function(b){if(e[b])e[b][g]()})}};b.colorSeriesMixin={pointArrayMap:["value"],axisTypes:["xAxis","yAxis","colorAxis"],optionalAxis:"colorAxis",trackerGroups:["group","markerGroup","dataLabelsGroup"],getSymbol:m,parallelArrays:["x","y","value"],colorKey:"value",pointAttribs:k.column.prototype.pointAttribs,
translateColors:function(){var b=this,g=this.options.nullColor,k=this.colorAxis,m=this.colorKey;r(this.data,function(e){var h=e[m];if(h=e.options.color||(e.isNull?g:k&&void 0!==h?k.toColor(h,e):e.color||b.color))e.color=h})},colorAttribs:function(b){var e={};g(b.color)&&(e[this.colorProp||"fill"]=b.color);return e}}})(n);(function(b){var g=b.colorPointMixin,r=b.each,m=b.merge,k=b.noop,e=b.pick,n=b.Series,w=b.seriesType,p=b.seriesTypes;w("heatmap","scatter",{animation:!1,borderWidth:0,nullColor:"#f7f7f7",
dataLabels:{formatter:function(){return this.point.value},inside:!0,verticalAlign:"middle",crop:!1,overflow:!1,padding:0},marker:null,pointRange:null,tooltip:{pointFormat:"{point.x}, {point.y}: {point.value}\x3cbr/\x3e"},states:{normal:{animation:!0},hover:{halo:!1,brightness:.2}}},m(b.colorSeriesMixin,{pointArrayMap:["y","value"],hasPointSpecificOptions:!0,supportsDrilldown:!0,getExtremesFromAll:!0,directTouch:!0,init:function(){var b;p.scatter.prototype.init.apply(this,arguments);b=this.options;
b.pointRange=e(b.pointRange,b.colsize||1);this.yAxis.axisPointRange=b.rowsize||1},translate:function(){var b=this.options,e=this.xAxis,g=this.yAxis,k=function(b,a,c){return Math.min(Math.max(a,b),c)};this.generatePoints();r(this.points,function(h){var a=(b.colsize||1)/2,c=(b.rowsize||1)/2,d=k(Math.round(e.len-e.translate(h.x-a,0,1,0,1)),-e.len,2*e.len),a=k(Math.round(e.len-e.translate(h.x+a,0,1,0,1)),-e.len,2*e.len),f=k(Math.round(g.translate(h.y-c,0,1,0,1)),-g.len,2*g.len),c=k(Math.round(g.translate(h.y+
c,0,1,0,1)),-g.len,2*g.len);h.plotX=h.clientX=(d+a)/2;h.plotY=(f+c)/2;h.shapeType="rect";h.shapeArgs={x:Math.min(d,a),y:Math.min(f,c),width:Math.abs(a-d),height:Math.abs(c-f)}});this.translateColors()},drawPoints:function(){p.column.prototype.drawPoints.call(this);r(this.points,function(b){b.graphic.attr(this.colorAttribs(b,b.state))},this)},animate:k,getBox:k,drawLegendSymbol:b.LegendSymbolMixin.drawRectangle,alignDataLabel:p.column.prototype.alignDataLabel,getExtremes:function(){n.prototype.getExtremes.call(this,
this.valueData);this.valueMin=this.dataMin;this.valueMax=this.dataMax;n.prototype.getExtremes.call(this)}}),g)})(n)});
