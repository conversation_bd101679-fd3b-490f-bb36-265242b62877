<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
  "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">


<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    
    <title>Glossary &#8212; phpMyAdmin 4.8.5 documentation</title>
    
    <link rel="stylesheet" href="_static/classic.css" type="text/css" />
    <link rel="stylesheet" href="_static/pygments.css" type="text/css" />
    
    <script type="text/javascript">
      var DOCUMENTATION_OPTIONS = {
        URL_ROOT:    './',
        VERSION:     '4.8.5',
        COLLAPSE_INDEX: false,
        FILE_SUFFIX: '.html',
        HAS_SOURCE:  true
      };
    </script>
    <script type="text/javascript" src="_static/jquery.js"></script>
    <script type="text/javascript" src="_static/underscore.js"></script>
    <script type="text/javascript" src="_static/doctools.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="copyright" title="Copyright" href="copyright.html" />
    <link rel="top" title="phpMyAdmin 4.8.5 documentation" href="index.html" />
    <link rel="prev" title="Credits" href="credits.html" /> 
  </head>
  <body role="document">
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="credits.html" title="Credits"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">phpMyAdmin 4.8.5 documentation</a> &#187;</li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <div class="section" id="glossary">
<span id="id1"></span><h1>Glossary<a class="headerlink" href="#glossary" title="Permalink to this headline">¶</a></h1>
<p>From Wikipedia, the free encyclopedia</p>
<dl class="glossary docutils">
<dt id="term-htaccess">.htaccess</dt>
<dd><p class="first">the default name of Apache&#8217;s directory-level configuration file.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/.htaccess">https://en.wikipedia.org/wiki/.htaccess</a>&gt;</p>
</div>
</dd>
<dt id="term-acl">ACL</dt>
<dd>Access Contol List</dd>
<dt id="term-blowfish">Blowfish</dt>
<dd><p class="first">a keyed, symmetric block cipher, designed in 1993 by Bruce Schneier.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Blowfish_(cipher)">https://en.wikipedia.org/wiki/Blowfish_(cipher)</a>&gt;</p>
</div>
</dd>
<dt id="term-browser">Browser</dt>
<dd><p class="first">a software application that enables a user to display and interact with text, images, and other information typically located on a web page at a website on the World Wide Web.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Web_browser">https://en.wikipedia.org/wiki/Web_browser</a>&gt;</p>
</div>
</dd>
<dt id="term-bzip2">bzip2</dt>
<dd><p class="first">a free software/open source data compression algorithm and program developed by Julian Seward.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Bzip2">https://en.wikipedia.org/wiki/Bzip2</a>&gt;</p>
</div>
</dd>
<dt id="term-cgi">CGI</dt>
<dd><p class="first">Common Gateway Interface is an important World Wide Web technology that
enables a client web browser to request data from a program executed on
the Web server.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Common_Gateway_Interface">https://en.wikipedia.org/wiki/Common_Gateway_Interface</a>&gt;</p>
</div>
</dd>
<dt id="term-changelog">Changelog</dt>
<dd><p class="first">a log or record of changes made to a project.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Changelog">https://en.wikipedia.org/wiki/Changelog</a>&gt;</p>
</div>
</dd>
<dt id="term-client">Client</dt>
<dd><p class="first">a computer system that accesses a (remote) service on another computer by some kind of network.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Client_(computing)">https://en.wikipedia.org/wiki/Client_(computing)</a>&gt;</p>
</div>
</dd>
<dt id="term-column">column</dt>
<dd><p class="first">a set of data values of a particular simple type, one for each row of the table.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Column_(database)">https://en.wikipedia.org/wiki/Column_(database)</a>&gt;</p>
</div>
</dd>
<dt id="term-cookie">Cookie</dt>
<dd><p class="first">a packet of information sent by a server to a World Wide Web browser and then sent back by the browser each time it accesses that server.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/HTTP_cookie">https://en.wikipedia.org/wiki/HTTP_cookie</a>&gt;</p>
</div>
</dd>
<dt id="term-csv">CSV</dt>
<dd><p class="first">Comma- separated values</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Comma-separated_values">https://en.wikipedia.org/wiki/Comma-separated_values</a>&gt;</p>
</div>
</dd>
<dt id="term-db">DB</dt>
<dd>look at <a class="reference internal" href="#term-database"><span class="xref std std-term">database</span></a></dd>
<dt id="term-database">database</dt>
<dd><p class="first">an organized collection of data.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Database">https://en.wikipedia.org/wiki/Database</a>&gt;</p>
</div>
</dd>
<dt id="term-engine">Engine</dt>
<dd>look at <a class="reference internal" href="#term-storage-engines"><span class="xref std std-term">storage engines</span></a></dd>
<dt id="term-extension">extension</dt>
<dd><p class="first">a PHP module that extends PHP with additional functionality.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Software_extension">https://en.wikipedia.org/wiki/Software_extension</a>&gt;</p>
</div>
</dd>
<dt id="term-faq">FAQ</dt>
<dd><p class="first">Frequently Asked Questions is a list of commonly asked question and there
answers.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/FAQ">https://en.wikipedia.org/wiki/FAQ</a>&gt;</p>
</div>
</dd>
<dt id="term-field">Field</dt>
<dd><p class="first">one part of divided data/columns.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Field_(computer_science)">https://en.wikipedia.org/wiki/Field_(computer_science)</a>&gt;</p>
</div>
</dd>
<dt id="term-foreign-key">foreign key</dt>
<dd><p class="first">a column or group of columns in a database row that point to a key column
or group of columns forming a key of another database row in some
(usually different) table.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Foreign_key">https://en.wikipedia.org/wiki/Foreign_key</a>&gt;</p>
</div>
</dd>
<dt id="term-gd">GD</dt>
<dd><p class="first">Graphics Library by Thomas Boutell and others for dynamically manipulating images.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/GD_Graphics_Library">https://en.wikipedia.org/wiki/GD_Graphics_Library</a>&gt;</p>
</div>
</dd>
<dt id="term-gd2">GD2</dt>
<dd>look at <a class="reference internal" href="#term-gd"><span class="xref std std-term">gd</span></a></dd>
<dt id="term-gzip">gzip</dt>
<dd><p class="first">gzip is short for GNU zip, a GNU free software file compression program.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Gzip">https://en.wikipedia.org/wiki/Gzip</a>&gt;</p>
</div>
</dd>
<dt id="term-host">host</dt>
<dd><p class="first">any machine connected to a computer network, a node that has a hostname.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Host">https://en.wikipedia.org/wiki/Host</a>&gt;</p>
</div>
</dd>
<dt id="term-hostname">hostname</dt>
<dd><p class="first">the unique name by which a network attached device is known on a network.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Hostname">https://en.wikipedia.org/wiki/Hostname</a>&gt;</p>
</div>
</dd>
<dt id="term-http">HTTP</dt>
<dd><p class="first">HyperText Transfer Protocol is the primary method used to transfer or
convey information on the World Wide Web.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/HyperText_Transfer_Protocol">https://en.wikipedia.org/wiki/HyperText_Transfer_Protocol</a>&gt;</p>
</div>
</dd>
<dt id="term-https">https</dt>
<dd><p class="first">a <a class="reference internal" href="#term-http"><span class="xref std std-term">HTTP</span></a>-connection with additional security measures.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Https:_URI_scheme">https://en.wikipedia.org/wiki/Https:_URI_scheme</a>&gt;</p>
</div>
</dd>
<dt id="term-iec">IEC</dt>
<dd>International Electrotechnical Commission</dd>
<dt id="term-iis">IIS</dt>
<dd><p class="first">Internet Information Services is a set of Internet-based services for
servers using Microsoft Windows.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Internet_Information_Services">https://en.wikipedia.org/wiki/Internet_Information_Services</a>&gt;</p>
</div>
</dd>
<dt id="term-index">Index</dt>
<dd><p class="first">a feature that allows quick access to the rows in a table.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Index_(database)">https://en.wikipedia.org/wiki/Index_(database)</a>&gt;</p>
</div>
</dd>
<dt id="term-ip">IP</dt>
<dd><p class="first">Internet Protocol is a data-oriented protocol used by source and
destination hosts for communicating data across a packet-switched
internetwork.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Internet_Protocol">https://en.wikipedia.org/wiki/Internet_Protocol</a>&gt;</p>
</div>
</dd>
<dt id="term-ip-address">IP Address</dt>
<dd><p class="first">a unique number that devices use in order to identify and communicate with each other on a network utilizing the Internet Protocol standard.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/IP_Address">https://en.wikipedia.org/wiki/IP_Address</a>&gt;</p>
</div>
</dd>
<dt id="term-ipv6">IPv6</dt>
<dd><p class="first">IPv6 (Internet Protocol version 6) is the latest revision of the
Internet Protocol (<a class="reference internal" href="#term-ip"><span class="xref std std-term">IP</span></a>), designed to deal with the
long-anticipated problem of its precedessor IPv4 running out of addresses.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/IPv6">https://en.wikipedia.org/wiki/IPv6</a>&gt;</p>
</div>
</dd>
<dt id="term-isapi">ISAPI</dt>
<dd><p class="first">Internet Server Application Programming Interface is the API of Internet Information Services (IIS).</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/ISAPI">https://en.wikipedia.org/wiki/ISAPI</a>&gt;</p>
</div>
</dd>
<dt id="term-isp">ISP</dt>
<dd><p class="first">Internet service provider is a business or organization that offers users
access to the Internet and related services.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/ISP">https://en.wikipedia.org/wiki/ISP</a>&gt;</p>
</div>
</dd>
<dt id="term-iso">ISO</dt>
<dd>International Standards Organisation</dd>
<dt id="term-jpeg">JPEG</dt>
<dd><p class="first">a most commonly used standard method of lossy compression for photographic images.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/JPEG">https://en.wikipedia.org/wiki/JPEG</a>&gt;</p>
</div>
</dd>
<dt id="term-jpg">JPG</dt>
<dd>look at <a class="reference internal" href="#term-jpeg"><span class="xref std std-term">jpeg</span></a></dd>
<dt id="term-key">Key</dt>
<dd>look at <a class="reference internal" href="#term-index"><span class="xref std std-term">index</span></a></dd>
<dt id="term-latex">LATEX</dt>
<dd><p class="first">a document preparation system for the TEX typesetting program.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/LaTeX">https://en.wikipedia.org/wiki/LaTeX</a>&gt;</p>
</div>
</dd>
<dt id="term-mac">Mac</dt>
<dd><p class="first">Apple Macintosh is line of personal computers is designed, developed, manufactured, and marketed by Apple Computer.</p>
<p class="last">. seealso:: &lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Mac">https://en.wikipedia.org/wiki/Mac</a>&gt;</p>
</dd>
<dt id="term-mac-os-x">Mac OS X</dt>
<dd><p class="first">the operating system which is included with all currently shipping Apple Macintosh computers in the consumer and professional markets.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Mac_OS_X">https://en.wikipedia.org/wiki/Mac_OS_X</a>&gt;</p>
</div>
</dd>
<dt id="term-mbstring">mbstring</dt>
<dd><p class="first">The PHP <cite>mbstring</cite> functions provide support for languages represented by multi-byte character sets, most notably UTF-8.</p>
<p>If you have troubles installing this extension, please follow <a class="reference internal" href="faq.html#faqmysql"><span class="std std-ref">1.20 I receive an error about missing mysqli and mysql extensions.</span></a>, it provides useful hints.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://secure.php.net/manual/en/book.mbstring.php">https://secure.php.net/manual/en/book.mbstring.php</a>&gt;</p>
</div>
</dd>
<dt id="term-mcrypt">MCrypt</dt>
<dd><p class="first">a cryptographic library.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/MCrypt">https://en.wikipedia.org/wiki/MCrypt</a>&gt;</p>
</div>
</dd>
<dt id="term-42">mcrypt</dt>
<dd><p class="first">the MCrypt PHP extension.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://secure.php.net/mcrypt">https://secure.php.net/mcrypt</a>&gt;</p>
</div>
</dd>
<dt id="term-mime">MIME</dt>
<dd><p class="first">Multipurpose Internet Mail Extensions is
an Internet Standard for the format of e-mail.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/MIME">https://en.wikipedia.org/wiki/MIME</a>&gt;</p>
</div>
</dd>
<dt id="term-module">module</dt>
<dd><p class="first">some sort of extension for the Apache Webserver.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Apache_HTTP_Server">https://en.wikipedia.org/wiki/Apache_HTTP_Server</a>&gt;</p>
</div>
</dd>
<dt id="term-mod-proxy-fcgi">mod_proxy_fcgi</dt>
<dd>an Apache module implmenting a Fast CGI interface; PHP can be run as a CGI module, FastCGI, or
directly as an Apache module.</dd>
<dt id="term-mysql">MySQL</dt>
<dd><p class="first">a multithreaded, multi-user, SQL (Structured Query Language) Database Management System (DBMS).</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/MySQL">https://en.wikipedia.org/wiki/MySQL</a>&gt;</p>
</div>
</dd>
<dt id="term-mysqli">mysqli</dt>
<dd><p class="first">the improved MySQL client PHP extension.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://secure.php.net/manual/en/book.mysqli.php">https://secure.php.net/manual/en/book.mysqli.php</a>&gt;</p>
</div>
</dd>
<dt id="term-48">mysql</dt>
<dd><p class="first">the MySQL client PHP extension.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://secure.php.net/manual/en/book.mysql.php">https://secure.php.net/manual/en/book.mysql.php</a>&gt;</p>
</div>
</dd>
<dt id="term-opendocument">OpenDocument</dt>
<dd><p class="first">open standard for office documents.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/OpenDocument">https://en.wikipedia.org/wiki/OpenDocument</a>&gt;</p>
</div>
</dd>
<dt id="term-os-x">OS X</dt>
<dd><p class="first">look at <a class="reference internal" href="#term-mac-os-x"><span class="xref std std-term">Mac OS X</span></a>.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/OS_X">https://en.wikipedia.org/wiki/OS_X</a>&gt;</p>
</div>
</dd>
<dt id="term-pdf">PDF</dt>
<dd><p class="first">Portable Document Format is a file format developed by Adobe Systems for
representing two dimensional documents in a device independent and
resolution independent format.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Portable_Document_Format">https://en.wikipedia.org/wiki/Portable_Document_Format</a>&gt;</p>
</div>
</dd>
<dt id="term-pear">PEAR</dt>
<dd><p class="first">the PHP Extension and Application Repository.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://pear.php.net/">https://pear.php.net/</a>&gt;</p>
</div>
</dd>
<dt id="term-pcre">PCRE</dt>
<dd><p class="first">Perl Compatible Regular Expressions is the perl-compatible regular
expression functions for PHP</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://secure.php.net/pcre">https://secure.php.net/pcre</a>&gt;</p>
</div>
</dd>
<dt id="term-php">PHP</dt>
<dd><p class="first">short for &#8220;PHP: Hypertext Preprocessor&#8221;, is an open-source, reflective
programming language used mainly for developing server-side applications
and dynamic web content, and more recently, a broader range of software
applications.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/PHP">https://en.wikipedia.org/wiki/PHP</a>&gt;</p>
</div>
</dd>
<dt id="term-port">port</dt>
<dd><p class="first">a connection through which data is sent and received.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Port_(computing)">https://en.wikipedia.org/wiki/Port_(computing)</a>&gt;</p>
</div>
</dd>
<dt id="term-primary-key">primary key</dt>
<dd><p class="first">A primary key is an index over one or more fields in a table with
unique values for each single row in this table. Every table should have
a primary key for easier accessing/identifying data in this table.  There
can only be one primary key per table and it is named always <strong>PRIMARY</strong>.
In fact a primary key is just an <a class="reference internal" href="#term-unique-key"><span class="xref std std-term">unique key</span></a> with the name
<strong>PRIMARY</strong>.  If no primary key is defined MySQL will use first <em>unique
key</em> as primary key if there is one.</p>
<p>You can create the primary key when creating the table (in phpMyAdmin
just check the primary key radio buttons for each field you wish to be
part of the primary key).</p>
<p class="last">You can also add a primary key to an existing table with <cite>ALTER</cite> <cite>TABLE</cite>
or <cite>CREATE</cite> <cite>INDEX</cite> (in phpMyAdmin you can just click on &#8216;add index&#8217; on
the table structure page below the listed fields).</p>
</dd>
<dt id="term-rfc">RFC</dt>
<dd><p class="first">Request for Comments (RFC) documents are a series of memoranda
encompassing new research, innovations, and methodologies applicable to
Internet technologies.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Request_for_Comments">https://en.wikipedia.org/wiki/Request_for_Comments</a>&gt;</p>
</div>
</dd>
<dt id="term-rfc-1952">RFC 1952</dt>
<dd><p class="first">GZIP file format specification version 4.3</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last"><span class="target" id="index-0"></span><a class="rfc reference external" href="https://tools.ietf.org/html/rfc1952.html"><strong>RFC 1952</strong></a></p>
</div>
</dd>
<dt id="term-row-record-tuple">Row (record, tuple)</dt>
<dd><p class="first">represents a single, implicitly structured data item in a table.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Row_(database)">https://en.wikipedia.org/wiki/Row_(database)</a>&gt;</p>
</div>
</dd>
<dt id="term-server">Server</dt>
<dd><p class="first">a computer system that provides services to other computing systems over a network.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Server_(computing)">https://en.wikipedia.org/wiki/Server_(computing)</a>&gt;</p>
</div>
</dd>
<dt id="term-storage-engines">Storage Engines</dt>
<dd><p class="first">MySQL can use several different formats for storing data on disk, these
are called storage engines or table types. phpMyAdmin allows a user to
change their storage engine for a particular table through the operations
tab.</p>
<p>Common table types are InnoDB and MyISAM, though many others exist and
may be desirable in some situations.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://dev.mysql.com/doc/refman/5.7/en/storage-engines.html">https://dev.mysql.com/doc/refman/5.7/en/storage-engines.html</a>&gt;</p>
</div>
</dd>
<dt id="term-socket">socket</dt>
<dd><p class="first">a form of inter-process communication.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Unix_domain_socket">https://en.wikipedia.org/wiki/Unix_domain_socket</a>&gt;</p>
</div>
</dd>
<dt id="term-ssl">SSL</dt>
<dd><p class="first">Secure Sockets Layer is a cryptographic protocol which provides secure
communication on the Internet.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Secure_Sockets_Layer">https://en.wikipedia.org/wiki/Secure_Sockets_Layer</a>&gt;</p>
</div>
</dd>
<dt id="term-stored-procedure">Stored procedure</dt>
<dd><p class="first">a subroutine available to applications accessing a relational database system</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Stored_procedure">https://en.wikipedia.org/wiki/Stored_procedure</a>&gt;</p>
</div>
</dd>
<dt id="term-sql">SQL</dt>
<dd><p class="first">Structured Query Language</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/SQL">https://en.wikipedia.org/wiki/SQL</a>&gt;</p>
</div>
</dd>
<dt id="term-table">table</dt>
<dd><p class="first">a set of data elements (cells) that is organized, defined and stored as
horizontal rows and vertical columns where each item can be uniquely
identified by a label or key or by it?s position in relation to other
items.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Table_(database)">https://en.wikipedia.org/wiki/Table_(database)</a>&gt;</p>
</div>
</dd>
<dt id="term-tar">tar</dt>
<dd><p class="first">a type of archive file format: the Tape ARchive format.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Tar_(file_format)">https://en.wikipedia.org/wiki/Tar_(file_format)</a>&gt;</p>
</div>
</dd>
<dt id="term-tcp">TCP</dt>
<dd><p class="first">Transmission Control Protocol is one of the core protocols of the
Internet protocol suite.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/TCP">https://en.wikipedia.org/wiki/TCP</a>&gt;</p>
</div>
</dd>
<dt id="term-tcpdf">TCPDF</dt>
<dd><p class="first">PHP library to generate PDF files.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://tcpdf.org/">https://tcpdf.org/</a>&gt;</p>
</div>
</dd>
<dt id="term-trigger">trigger</dt>
<dd><p class="first">a procedural code that is automatically executed in response to certain events on a particular table or view in a database</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Database_trigger">https://en.wikipedia.org/wiki/Database_trigger</a>&gt;</p>
</div>
</dd>
<dt id="term-unique-key">unique key</dt>
<dd>An unique key is an index over one or more fields in a table which has a
unique value for each row.  The first unique key will be treated as
<a class="reference internal" href="#term-primary-key"><span class="xref std std-term">primary key</span></a> if there is no <em>primary key</em> defined.</dd>
<dt id="term-url">URL</dt>
<dd><p class="first">Uniform Resource Locator is a sequence of characters, conforming to a
standardized format, that is used for referring to resources, such as
documents and images on the Internet, by their location.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/URL">https://en.wikipedia.org/wiki/URL</a>&gt;</p>
</div>
</dd>
<dt id="term-webserver">Webserver</dt>
<dd><p class="first">A computer (program) that is responsible for accepting HTTP requests from clients and serving them Web pages.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Webserver">https://en.wikipedia.org/wiki/Webserver</a>&gt;</p>
</div>
</dd>
<dt id="term-xml">XML</dt>
<dd><p class="first">Extensible Markup Language is a W3C-recommended general- purpose markup
language for creating special-purpose markup languages, capable of
describing many different kinds of data.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/XML">https://en.wikipedia.org/wiki/XML</a>&gt;</p>
</div>
</dd>
<dt id="term-zip">ZIP</dt>
<dd><p class="first">a popular data compression and archival format.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/ZIP_(file_format)">https://en.wikipedia.org/wiki/ZIP_(file_format)</a>&gt;</p>
</div>
</dd>
<dt id="term-zlib">zlib</dt>
<dd><p class="first">an open-source, cross- platform data compression library by Jean-loup Gailly and Mark Adler.</p>
<div class="last admonition seealso">
<p class="first admonition-title">See also</p>
<p class="last">&lt;<a class="reference external" href="https://en.wikipedia.org/wiki/Zlib">https://en.wikipedia.org/wiki/Zlib</a>&gt;</p>
</div>
</dd>
</dl>
</div>


          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <h4>Previous topic</h4>
  <p class="topless"><a href="credits.html"
                        title="previous chapter">Credits</a></p>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="_sources/glossary.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3>Quick search</h3>
    <form class="search" action="search.html" method="get">
      <div><input type="text" name="q" /></div>
      <div><input type="submit" value="Go" /></div>
      <input type="hidden" name="check_keywords" value="yes" />
      <input type="hidden" name="area" value="default" />
    </form>
</div>
<script type="text/javascript">$('#searchbox').show(0);</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="credits.html" title="Credits"
             >previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">phpMyAdmin 4.8.5 documentation</a> &#187;</li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
        &#169; <a href="copyright.html">Copyright</a> 2012 - 2018, The phpMyAdmin devel team.
      Created using <a href="http://sphinx-doc.org/">Sphinx</a> 1.4.9.
    </div>
  </body>
</html>