<li>
    <input type="checkbox" name="onserver" value="saveit"
        id="checkbox_dump_onserver"{{ export_is_checked ? ' checked' }}>
    <label for="checkbox_dump_onserver">
        {{ 'Save on server in the directory <strong>%s</strong>'|trans|format(save_dir|e)|raw }}
    </label>
</li>
<li>
    <input type="checkbox" name="onserver_overwrite"
        value="saveitover" id="checkbox_dump_onserver_overwrite"
        {{- export_overwrite_is_checked ? ' checked' }}>
    <label for="checkbox_dump_onserver_overwrite">
        {% trans 'Overwrite existing file(s)' %}
    </label>
</li>
