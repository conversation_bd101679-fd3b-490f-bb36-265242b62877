<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
  "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">


<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    
    <title>Configuring phpMyAdmin &#8212; phpMyAdmin 4.8.5 documentation</title>
    
    <link rel="stylesheet" href="_static/classic.css" type="text/css" />
    <link rel="stylesheet" href="_static/pygments.css" type="text/css" />
    
    <script type="text/javascript">
      var DOCUMENTATION_OPTIONS = {
        URL_ROOT:    './',
        VERSION:     '4.8.5',
        COLLAPSE_INDEX: false,
        FILE_SUFFIX: '.html',
        HAS_SOURCE:  true
      };
    </script>
    <script type="text/javascript" src="_static/jquery.js"></script>
    <script type="text/javascript" src="_static/underscore.js"></script>
    <script type="text/javascript" src="_static/doctools.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="copyright" title="Copyright" href="copyright.html" />
    <link rel="top" title="phpMyAdmin 4.8.5 documentation" href="index.html" />
    <link rel="up" title="User Guide" href="user.html" />
    <link rel="next" title="Two-factor authentication" href="two_factor.html" />
    <link rel="prev" title="User Guide" href="user.html" /> 
  </head>
  <body role="document">
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="two_factor.html" title="Two-factor authentication"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="user.html" title="User Guide"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">phpMyAdmin 4.8.5 documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="user.html" accesskey="U">User Guide</a> &#187;</li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <div class="section" id="configuring-phpmyadmin">
<h1>Configuring phpMyAdmin<a class="headerlink" href="#configuring-phpmyadmin" title="Permalink to this headline">¶</a></h1>
<p>There are many configuration settings that can be used to customize the
interface. Those settings are described in
<a class="reference internal" href="config.html#config"><span class="std std-ref">Configuration</span></a>. There are several layers of the configuration.</p>
<p>The global settings can be configured in <code class="file docutils literal"><span class="pre">config.inc.php</span></code> as described in
<a class="reference internal" href="config.html#config"><span class="std std-ref">Configuration</span></a>. This is only way to configure connections to databases and other
system wide settings.</p>
<p>On top of this there are user settings which can be persistently stored in
<a class="reference internal" href="setup.html#linked-tables"><span class="std std-ref">phpMyAdmin configuration storage</span></a>, possibly automatically configured through
<a class="reference internal" href="setup.html#zeroconf"><span class="std std-ref">Zero configuration</span></a>.  If the <a class="reference internal" href="setup.html#linked-tables"><span class="std std-ref">phpMyAdmin configuration storage</span></a> are not configured, the settings
are temporarily stored in the session data; these are valid only until you
logout.</p>
<p>You can also save the user configuration for further use, either download them
as a file or to the browser local storage. You can find both those options in
the <span class="guilabel">Settings</span> tab. The settings stored in browser local storage will
be automatically offered for loading upon your login to phpMyAdmin.</p>
</div>


          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <h4>Previous topic</h4>
  <p class="topless"><a href="user.html"
                        title="previous chapter">User Guide</a></p>
  <h4>Next topic</h4>
  <p class="topless"><a href="two_factor.html"
                        title="next chapter">Two-factor authentication</a></p>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="_sources/settings.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3>Quick search</h3>
    <form class="search" action="search.html" method="get">
      <div><input type="text" name="q" /></div>
      <div><input type="submit" value="Go" /></div>
      <input type="hidden" name="check_keywords" value="yes" />
      <input type="hidden" name="area" value="default" />
    </form>
</div>
<script type="text/javascript">$('#searchbox').show(0);</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="two_factor.html" title="Two-factor authentication"
             >next</a> |</li>
        <li class="right" >
          <a href="user.html" title="User Guide"
             >previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">phpMyAdmin 4.8.5 documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="user.html" >User Guide</a> &#187;</li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
        &#169; <a href="copyright.html">Copyright</a> 2012 - 2018, The phpMyAdmin devel team.
      Created using <a href="http://sphinx-doc.org/">Sphinx</a> 1.4.9.
    </div>
  </body>
</html>